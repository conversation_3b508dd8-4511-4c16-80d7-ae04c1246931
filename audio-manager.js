// 音频管理器
class AudioManager {
    constructor() {
        this.sounds = {};
        this.enabled = true;
        this.volume = 0.5;
        this.loadSounds();
    }

    // 加载所有音效
    loadSounds() {
        const soundFiles = {
            move: 'sounds/move.mp3',
            shoot: 'sounds/shoot.mp3',
            hit: 'sounds/hit.mp3',
            cardFlip: 'sounds/card-flip.mp3',
            collect: 'sounds/collect.mp3',
            damage: 'sounds/damage.mp3',
            death: 'sounds/death.mp3',
            resurrect: 'sounds/resurrect.mp3',
            freeze: 'sounds/freeze.mp3',
            invincible: 'sounds/invincible.mp3'
        };

        for (const [name, path] of Object.entries(soundFiles)) {
            this.sounds[name] = new Audio(path);
            this.sounds[name].volume = this.volume;
            this.sounds[name].preload = 'auto';
            
            // 处理加载错误
            this.sounds[name].onerror = () => {
                console.warn(`无法加载音效: ${path}`);
            };
        }
    }

    // 播放音效
    play(soundName, volume = 1.0) {
        if (!this.enabled || !this.sounds[soundName]) {
            return;
        }

        try {
            const sound = this.sounds[soundName].cloneNode();
            sound.volume = this.volume * volume;
            sound.play().catch(e => {
                console.warn(`播放音效失败: ${soundName}`, e);
            });
        } catch (e) {
            console.warn(`播放音效失败: ${soundName}`, e);
        }
    }

    // 设置总音量
    setVolume(volume) {
        this.volume = Math.max(0, Math.min(1, volume));
        for (const sound of Object.values(this.sounds)) {
            sound.volume = this.volume;
        }
    }

    // 开启/关闭音效
    toggle() {
        this.enabled = !this.enabled;
        return this.enabled;
    }

    // 停止所有音效
    stopAll() {
        for (const sound of Object.values(this.sounds)) {
            sound.pause();
            sound.currentTime = 0;
        }
    }
}

// 创建全局音频管理器实例
const audioManager = new AudioManager();

// 导出音频管理器
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AudioManager;
}