# 大尖牙游戏 - APK打包指南

## 项目概述

「大尖牙」是一个有趣的双人对战Web游戏，本项目提供了将其打包成Android APK应用的完整解决方案，让您可以在平板电脑等移动设备上运行游戏。

## 文件说明

本项目包含以下文件：

1. **原始游戏文件**
   - `index.html` - 原始Web游戏的HTML文件
   - `game.js` - 游戏的主要JavaScript代码
   - `style.css` - 游戏的样式表
   - `images/` - 游戏使用的图片资源

2. **移动设备适配文件**
   - `index-mobile.html` - 针对移动设备优化的HTML文件
   - `mobile-controls.js` - 为触摸屏设备添加的虚拟控制按钮

3. **打包相关文件**
   - `config.xml` - Cordova配置文件模板
   - `打包脚本.sh` - 自动化打包脚本
   - `打包指南.md` - 详细的打包步骤说明
   - `README.md` - 本文件，项目概述

## 快速开始

### 准备工作

在开始打包之前，您需要安装以下工具：

- **Node.js 和 npm** - JavaScript运行环境和包管理器
- **Cordova** - 用于将Web应用打包成移动应用的框架
- **Java Development Kit (JDK)** - Android应用开发必需
- **Android Studio** - 包含Android SDK和模拟器

详细的安装说明请参考 `打包指南.md`。

### 打包步骤

1. **使用自动化脚本（推荐）**

   如果您已经安装了所有必要的工具，可以直接运行自动化脚本：

   ```bash
   chmod +x 打包脚本.sh
   ./打包脚本.sh
   ```

   脚本将自动检查环境、创建Cordova项目、复制游戏文件并构建APK。

2. **手动打包**

   如果您希望手动完成打包过程，请按照 `打包指南.md` 中的详细步骤操作。

## 移动设备适配

为了在移动设备上获得更好的游戏体验，我们进行了以下优化：

1. **触摸控制** - 添加了虚拟方向按钮，支持触摸屏操作
2. **屏幕适配** - 优化了界面布局，适应不同尺寸的屏幕
3. **设备集成** - 添加了Cordova设备就绪事件，确保游戏在移动设备上正常初始化

## 测试APK

打包完成后，您可以通过以下方式测试APK：

- 使用Android模拟器
- 将APK文件安装到实际的Android设备上

## 常见问题

**Q: 为什么我无法构建APK？**

A: 请确保您已正确安装并配置了所有必要的工具，特别是Android SDK和JDK。检查环境变量是否正确设置。

**Q: 游戏在移动设备上运行缓慢怎么办？**

A: 移动设备的处理能力有限，您可能需要优化游戏代码，减少动画效果或简化游戏元素。

**Q: 如何发布到Google Play商店？**

A: 请参考 `打包指南.md` 中的"签名和发布"部分，了解如何创建签名密钥并准备发布版APK。

## 贡献

欢迎提出建议和改进意见，一起让「大尖牙」游戏变得更好！

## 许可

本项目仅用于教育和个人使用。