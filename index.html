<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Justin的第一个游戏：大尖牙</title>
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="virtual-controls.css">
    <script src="https://cdn.jsdelivr.net/npm/webmidi@latest/dist/iife/webmidi.iife.js"></script>
    <script src="generate-audio.js"></script>
    <script src="audio-manager.js"></script>

</head>
<body>
    <div class="game-container">
        <div id="game-world">
            <!-- 游戏世界将在这里渲染 -->
            <div id="player1" class="player red-player">
                <div class="arm-left"></div>
                <div class="arm-right"></div>
                <div class="leg-left"></div>
                <div class="leg-right"></div>
                <div class="face">
                    <div class="eye-left"></div>
                    <div class="eye-right"></div>
                    <div class="mouth"></div>
                </div>
                <div class="launcher" id="launcher1"></div>
            </div>
            <div id="player2" class="player blue-player">
                <div class="arm-left"></div>
                <div class="arm-right"></div>
                <div class="leg-left"></div>
                <div class="leg-right"></div>
                <div class="face">
                    <div class="eye-left"></div>
                    <div class="eye-right"></div>
                    <div class="mouth"></div>
                </div>
                <div class="launcher" id="launcher2"></div>
            </div>
        </div>
        <div class="game-info">
            <h1>Justin的第一个游戏：合作大作战</h1>
            <div class="controls-info">
                <div class="player-controls">
                    <h2>红色玩家 (1P)</h2>
                    <p>使用方向键 ↑ ↓ ← → 移动</p>
                    <p>使用数字键 0 发射火球攻击敌人</p>
                </div>
                <div class="player-controls">
                    <h2>蓝色玩家 (2P)</h2>
                    <p>使用 WASD 键移动</p>
                    <p>使用空格键发射火球攻击敌人</p>
                </div>
                <div class="game-objective">
                    <h3>🎯 游戏目标</h3>
                    <p>两名玩家合作对抗随机出现的敌人：</p>
                    <ul>
                        <li>👹 怪兽 - 近战攻击，血量中等</li>
                        <li>💀 射箭骷髅 - 远程攻击，保持距离</li>
                        <li>🧟 僵尸 - 移动缓慢但血量很高</li>
                    </ul>
                    <p>收集卡片获得增益效果，团结一致击败所有敌人！</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 虚拟控制按钮 -->
    <div class="virtual-controls">
        <!-- 玩家1控制器（左侧） -->
        <div class="player-controls player1-controls">
            <div class="player-label" style="color: #FF5555;">红色玩家 (1P)</div>
            <div class="direction-buttons">
                <button class="direction-btn up-btn">↑</button>
                <button class="direction-btn left-btn">←</button>
                <button class="direction-btn right-btn">→</button>
                <button class="direction-btn down-btn">↓</button>
            </div>
            <div class="shoot-button-container">
                <button class="shoot-btn" style="border-color: #FF5555;">发射</button>
            </div>
        </div>

        <!-- 玩家2控制器（右侧） -->
        <div class="player-controls player2-controls">
            <div class="player-label" style="color: #5555FF; transform: rotate(180deg);">蓝色玩家 (2P)</div>
            <div class="direction-buttons">
                <button class="direction-btn up-btn">↑</button>
                <button class="direction-btn left-btn">←</button>
                <button class="direction-btn right-btn">→</button>
                <button class="direction-btn down-btn">↓</button>
            </div>
            <div class="shoot-button-container">
                <button class="shoot-btn" style="border-color: #5555FF; transform: rotate(180deg);">发射</button>
            </div>
        </div>
    </div>

    <script src="game.js"></script>
    <script src="virtual-controls.js"></script>
</body>
</html>