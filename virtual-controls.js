// 虚拟控制按钮的鼠标点击事件处理
document.addEventListener('DOMContentLoaded', function() {
    // 获取所有虚拟控制按钮
    setupVirtualControls();
});

function setupVirtualControls() {
    // 获取玩家状态对象（从game.js中获取）
    const player1State = window.player1State;
    const player2State = window.player2State;
    
    if (!player1State || !player2State) {
        console.error('无法获取玩家状态对象，虚拟控制按钮可能无法正常工作');
        return;
    }
    
    // 设置玩家1的控制按钮
    setupPlayerControls(1, player1State);
    
    // 设置玩家2的控制按钮
    setupPlayerControls(2, player2State);
    
    // 修复玩家2控制器的方向按钮显示问题
    fixPlayer2ButtonsDisplay();
}

function setupPlayerControls(playerNum, playerState) {
    const controlsSelector = playerNum === 1 ? '.player1-controls' : '.player2-controls';
    const controls = document.querySelector(controlsSelector);
    
    if (!controls) return;
    
    // 设置方向按钮
    setupDirectionButtons(controls, playerNum, playerState);
    
    // 设置发射按钮
    setupShootButton(controls, playerNum, playerState);
}

function setupDirectionButtons(controls, playerNum, playerState) {
    // 获取方向按钮
    const upBtn = controls.querySelector('.up-btn');
    const downBtn = controls.querySelector('.down-btn');
    const leftBtn = controls.querySelector('.left-btn');
    const rightBtn = controls.querySelector('.right-btn');
    
    // 为每个方向按钮添加鼠标事件
    if (upBtn) {
        upBtn.addEventListener('mousedown', function() {
            playerState.keys.up = true;
        });
        upBtn.addEventListener('mouseup', function() {
            playerState.keys.up = false;
        });
        upBtn.addEventListener('mouseleave', function() {
            playerState.keys.up = false;
        });
    }
    
    if (downBtn) {
        downBtn.addEventListener('mousedown', function() {
            playerState.keys.down = true;
        });
        downBtn.addEventListener('mouseup', function() {
            playerState.keys.down = false;
        });
        downBtn.addEventListener('mouseleave', function() {
            playerState.keys.down = false;
        });
    }
    
    if (leftBtn) {
        leftBtn.addEventListener('mousedown', function() {
            playerState.keys.left = true;
        });
        leftBtn.addEventListener('mouseup', function() {
            playerState.keys.left = false;
        });
        leftBtn.addEventListener('mouseleave', function() {
            playerState.keys.left = false;
        });
    }
    
    if (rightBtn) {
        rightBtn.addEventListener('mousedown', function() {
            playerState.keys.right = true;
        });
        rightBtn.addEventListener('mouseup', function() {
            playerState.keys.right = false;
        });
        rightBtn.addEventListener('mouseleave', function() {
            playerState.keys.right = false;
        });
    }
}

function setupShootButton(controls, playerNum, playerState) {
    // 获取发射按钮
    const shootBtn = controls.querySelector('.shoot-btn');
    
    if (shootBtn) {
        shootBtn.addEventListener('mousedown', function() {
            playerState.keys.shoot = true;
        });
        shootBtn.addEventListener('mouseup', function() {
            playerState.keys.shoot = false;
        });
        shootBtn.addEventListener('mouseleave', function() {
            playerState.keys.shoot = false;
        });
    }
}