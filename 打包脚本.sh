#!/bin/bash

# 大尖牙游戏打包脚本
# 此脚本将帮助您将Web游戏打包成APK应用

echo "===== 大尖牙游戏打包工具 ====="
echo "此脚本将引导您完成将Web游戏打包成APK的过程"
echo ""

# 检查是否安装了必要的工具
check_requirements() {
  echo "正在检查必要的工具..."
  
  # 检查Node.js
  if ! command -v node &> /dev/null; then
    echo "未安装Node.js。请先安装Node.js: https://nodejs.org/"
    exit 1
  else
    echo "✓ Node.js已安装: $(node -v)"
  fi
  
  # 检查npm
  if ! command -v npm &> /dev/null; then
    echo "未安装npm。请重新安装Node.js: https://nodejs.org/"
    exit 1
  else
    echo "✓ npm已安装: $(npm -v)"
  fi
  
  # 检查Cordova
  if ! command -v cordova &> /dev/null; then
    echo "未安装Cordova。正在安装..."
    npm install -g cordova
    if [ $? -ne 0 ]; then
      echo "Cordova安装失败。请手动运行: npm install -g cordova"
      exit 1
    fi
  else
    echo "✓ Cordova已安装: $(cordova -v)"
  fi
  
  # 检查Java
  if ! command -v java &> /dev/null; then
    echo "未安装Java。请安装JDK: https://www.oracle.com/java/technologies/javase-downloads.html"
    exit 1
  else
    echo "✓ Java已安装: $(java -version 2>&1 | head -n 1)"
  fi
  
  # 检查Android SDK
  if [ -z "$ANDROID_HOME" ]; then
    echo "未设置ANDROID_HOME环境变量。请安装Android Studio并设置环境变量。"
    echo "详情请参考: https://developer.android.com/studio/install"
    exit 1
  else
    echo "✓ Android SDK已配置: $ANDROID_HOME"
  fi
  
  echo "所有必要的工具已安装和配置。"
  echo ""
}

# 创建Cordova项目
create_cordova_project() {
  echo "正在创建Cordova项目..."
  
  # 创建项目目录
  PROJECT_DIR="DaJianYa"
  if [ -d "$PROJECT_DIR" ]; then
    echo "项目目录已存在。是否删除并重新创建? (y/n)"
    read answer
    if [ "$answer" = "y" ]; then
      rm -rf "$PROJECT_DIR"
    else
      echo "请备份或删除现有项目目录后重试。"
      exit 1
    fi
  fi
  
  # 创建新项目
  cordova create "$PROJECT_DIR" com.dajianya.game 大尖牙
  if [ $? -ne 0 ]; then
    echo "Cordova项目创建失败。"
    exit 1
  fi
  
  # 进入项目目录
  cd "$PROJECT_DIR"
  
  # 添加Android平台
  echo "正在添加Android平台..."
  cordova platform add android
  if [ $? -ne 0 ]; then
    echo "添加Android平台失败。请检查Android SDK配置。"
    exit 1
  fi
  
  echo "Cordova项目创建成功。"
  echo ""
}

# 复制游戏文件
copy_game_files() {
  echo "正在复制游戏文件到Cordova项目..."
  
  # 复制HTML、CSS和JavaScript文件
  cp -f "../index-mobile.html" "www/index.html"
  cp -f "../style.css" "www/style.css"
  cp -f "../game.js" "www/game.js"
  cp -f "../mobile-controls.js" "www/mobile-controls.js"
  
  # 复制图片目录
  mkdir -p "www/images"
  cp -rf "../images/"* "www/images/"
  
  # 复制配置文件
  cp -f "../config.xml" "config.xml"
  
  echo "游戏文件复制完成。"
  echo ""
}

# 构建APK
build_apk() {
  echo "正在构建APK..."
  
  # 检查构建要求
  echo "检查构建要求..."
  cordova requirements
  
  # 构建调试版APK
  echo "构建调试版APK..."
  cordova build android
  
  if [ $? -ne 0 ]; then
    echo "APK构建失败。请检查错误信息。"
    exit 1
  fi
  
  # 复制APK到上级目录
  APK_PATH="platforms/android/app/build/outputs/apk/debug/app-debug.apk"
  if [ -f "$APK_PATH" ]; then
    cp "$APK_PATH" "../大尖牙.apk"
    echo "APK已构建并复制到: ../大尖牙.apk"
  else
    echo "找不到构建的APK文件。请检查构建输出。"
    exit 1
  fi
  
  echo "APK构建完成。"
  echo ""
}

# 主函数
main() {
  check_requirements
  create_cordova_project
  copy_game_files
  build_apk
  
  echo "===== 打包过程完成 ====="
  echo "您的APK文件已生成: 大尖牙.apk"
  echo "您可以将此文件安装到Android设备上进行测试。"
  echo "如需发布到应用商店，请参考打包指南.md中的签名和发布说明。"
}

# 执行主函数
main