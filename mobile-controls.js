// 移动设备触摸控制
document.addEventListener('DOMContentLoaded', function() {
    // 检测是否为移动设备
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    
    if (isMobile) {
        // 创建虚拟控制器容器
        createVirtualControls();
    }
});

function createVirtualControls() {
    // 创建控制器容器
    const controlsContainer = document.createElement('div');
    controlsContainer.className = 'virtual-controls';
    document.body.appendChild(controlsContainer);
    
    // 创建玩家1控制器（左侧）
    const player1Controls = document.createElement('div');
    player1Controls.className = 'player-controls player1-controls';
    controlsContainer.appendChild(player1Controls);
    
    // 创建玩家2控制器（右侧）
    const player2Controls = document.createElement('div');
    player2Controls.className = 'player-controls player2-controls';
    controlsContainer.appendChild(player2Controls);
    
    // 添加方向按钮和发射按钮 - 玩家1
    addDirectionButtons(player1Controls, 1);
    addShootButton(player1Controls, 1);
    
    // 添加方向按钮和发射按钮 - 玩家2
    addDirectionButtons(player2Controls, 2);
    addShootButton(player2Controls, 2);
    
    // 添加控制器样式
    addControlsStyles();
}

function addDirectionButtons(container, playerNum) {
    const directions = ['up', 'down', 'left', 'right'];
    const symbols = ['↑', '↓', '←', '→'];
    
    // 创建按钮容器
    const buttonContainer = document.createElement('div');
    buttonContainer.className = 'direction-buttons';
    container.appendChild(buttonContainer);
    
    // 添加玩家标识
    const playerLabel = document.createElement('div');
    playerLabel.className = 'player-label';
    playerLabel.textContent = playerNum === 1 ? '红色玩家 (1P)' : '蓝色玩家 (2P)';
    playerLabel.style.color = playerNum === 1 ? '#FF5555' : '#5555FF';
    container.insertBefore(playerLabel, buttonContainer);
    
    // 创建方向按钮
    directions.forEach((direction, index) => {
        const button = document.createElement('button');
        button.className = `direction-btn ${direction}-btn`;
        button.textContent = symbols[index];
        buttonContainer.appendChild(button);
        
        // 添加触摸事件
        button.addEventListener('touchstart', function(e) {
            e.preventDefault();
            handleButtonPress(playerNum, direction, true);
        });
        
        button.addEventListener('touchend', function(e) {
            e.preventDefault();
            handleButtonPress(playerNum, direction, false);
        });
        
        // 添加触摸离开事件，确保按钮释放
        button.addEventListener('touchcancel', function(e) {
            e.preventDefault();
            handleButtonPress(playerNum, direction, false);
        });
    });
}

// 添加发射按钮
function addShootButton(container, playerNum) {
    // 创建发射按钮容器
    const shootContainer = document.createElement('div');
    shootContainer.className = 'shoot-button-container';
    container.appendChild(shootContainer);
    
    // 创建发射按钮
    const shootButton = document.createElement('button');
    shootButton.className = 'shoot-btn';
    shootButton.textContent = '发射';
    shootContainer.appendChild(shootButton);
    
    // 设置按钮颜色
    shootButton.style.borderColor = playerNum === 1 ? '#FF5555' : '#5555FF';
    
    // 添加触摸事件
    shootButton.addEventListener('touchstart', function(e) {
        e.preventDefault();
        handleButtonPress(playerNum, 'shoot', true);
    });
    
    shootButton.addEventListener('touchend', function(e) {
        e.preventDefault();
        handleButtonPress(playerNum, 'shoot', false);
    });
    
    shootButton.addEventListener('touchcancel', function(e) {
        e.preventDefault();
        handleButtonPress(playerNum, 'shoot', false);
    });
}

function handleButtonPress(playerNum, direction, isPressed) {
    // 获取对应的玩家状态
    const playerState = playerNum === 1 ? player1State : player2State;
    
    // 更新按键状态
    switch(direction) {
        case 'up':
            playerState.keys.up = isPressed;
            break;
        case 'down':
            playerState.keys.down = isPressed;
            break;
        case 'left':
            playerState.keys.left = isPressed;
            break;
        case 'right':
            playerState.keys.right = isPressed;
            break;
        case 'shoot':
            playerState.keys.shoot = isPressed;
            break;
    }
}

function addControlsStyles() {
    // 创建样式元素
    const styleElement = document.createElement('style');
    styleElement.textContent = `
        .virtual-controls {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            display: flex;
            justify-content: space-between;
            pointer-events: none; /* 允许点击穿透到游戏区域 */
            z-index: 1000;
        }
        
        .player-controls {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 20px;
            pointer-events: auto; /* 恢复按钮的点击事件 */
            width: 150px; /* 控制宽度 */
        }
        
        .player1-controls {
            position: absolute;
            left: 10px;
            top: 50%;
            transform: translateY(-50%);
        }
        
        .player2-controls {
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
        }
        
        .player-label {
            font-weight: bold;
            margin-bottom: 15px;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.7);
        }
        
        .player2-controls .player-label {
            transform: rotate(180deg);
        }
        
        .direction-buttons {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            grid-template-rows: repeat(3, 1fr);
            gap: 8px;
            margin-bottom: 20px;
        }
        
        .direction-btn {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            border: none;
            background-color: rgba(255, 255, 255, 0.7);
            font-size: 28px;
            display: flex;
            justify-content: center;
            align-items: center;
            -webkit-tap-highlight-color: transparent;
        }
        
        .up-btn {
            grid-column: 2;
            grid-row: 1;
        }
        
        .left-btn {
            grid-column: 1;
            grid-row: 2;
        }
        
        .right-btn {
            grid-column: 3;
            grid-row: 2;
        }
        
        .down-btn {
            grid-column: 2;
            grid-row: 3;
        }
        
        .shoot-button-container {
            width: 100%;
            display: flex;
            justify-content: center;
            margin-top: 10px;
        }
        
        .shoot-btn {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            border: none;
            background-color: rgba(255, 255, 255, 0.7);
            font-size: 20px;
            font-weight: bold;
            display: flex;
            justify-content: center;
            align-items: center;
            -webkit-tap-highlight-color: transparent;
        }
        
        .player2-controls .shoot-btn {
            transform: rotate(180deg);
        }
        
        .direction-btn:active,
        .shoot-btn:active {
            background-color: rgba(200, 200, 200, 0.9);
            transform: scale(0.95);
        }
        
        .player2-controls .direction-btn:active,
        .player2-controls .shoot-btn:active {
            transform: scale(0.95) rotate(180deg);
        }
        
        .player1-controls .direction-btn,
        .player1-controls .shoot-btn {
            border: 3px solid #FF5555;
        }
        
        .player2-controls .direction-btn,
        .player2-controls .shoot-btn {
            border: 3px solid #5555FF;
        }
        
        /* 适配不同屏幕尺寸 */
        @media (max-height: 600px) {
            .direction-btn {
                width: 50px;
                height: 50px;
                font-size: 24px;
            }
            
            .shoot-btn {
                width: 70px;
                height: 70px;
                font-size: 18px;
            }
        }
    `;
    document.head.appendChild(styleElement);
}