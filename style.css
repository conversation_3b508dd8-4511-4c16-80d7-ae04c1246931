* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background-color: #333;
    color: white;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    overflow: hidden;
}

.game-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    max-width: 1200px;
}

#game-world {
    position: relative;
    width: 1200px;
    height: 800px;
    background-color: #8BAF9C; /* 草地颜色 */
    border: 10px solid #5E4C3E; /* 木框 */
    overflow: hidden;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
    background-image: linear-gradient(#8BAF9C 8px, transparent 8px),
                      linear-gradient(90deg, #8BAF9C 8px, transparent 8px);
    background-size: 32px 32px; /* 网格大小 */
    image-rendering: pixelated;
}

.player {
    position: absolute;
    width: 32px;
    height: 48px;
    background-size: contain;
    background-repeat: no-repeat;
    transition: transform 0.1s;
    z-index: 10;
    /* 添加相对定位，方便添加手脚和脸部 */
    position: relative;
    transition: transform 0.3s ease-in-out, width 0.3s ease-in-out, height 0.3s ease-in-out, filter 0.3s ease-in-out; /* 添加过渡效果 */
    transform-origin: center center; /* 确保从中心缩放 */
}

/* 变大状态 */
.player.enlarged {
    transform: scale(2); /* 放大2倍 */
}

/* 无敌状态 */
.player.invincible {
    filter: brightness(1.5) drop-shadow(0 0 10px #FF6B6B); /* 发光效果 */
}

.red-player {
    background-color: #FF5555;
    border: 2px solid #AA3333;
    left: 100px;
    top: 100px;
}

.blue-player {
    background-color: #5555FF;
    border: 2px solid #3333AA;
    left: 200px;
    top: 100px;
}

/* 玩家手臂 */
.player .arm-left, .player .arm-right {
    position: absolute;
    width: 8px;
    height: 16px;
    background-color: inherit;
    border: 1px solid;
    border-color: inherit;
    z-index: 9;
}

.player .arm-left {
    left: -6px;
    top: 16px;
}

.player .arm-right {
    right: -6px;
    top: 16px;
}

/* 玩家腿部 */
.player .leg-left, .player .leg-right {
    position: absolute;
    width: 8px;
    height: 16px;
    background-color: inherit;
    border: 1px solid;
    border-color: inherit;
    bottom: -14px;
    z-index: 9;
}

.player .leg-left {
    left: 4px;
}

.player .leg-right {
    right: 4px;
}

/* 像素风格的头部 */
.player::before {
    content: '';
    position: absolute;
    width: 24px;
    height: 24px;
    top: -16px;
    left: 4px;
    background-color: inherit;
    border: 2px solid;
    border-color: inherit;
}

/* 脸部表情 */
.player .face {
    position: absolute;
    width: 24px;
    height: 12px;
    top: -10px;
    left: 4px;
    z-index: 11;
}

/* 眼睛 */
.player .eye-left, .player .eye-right {
    position: absolute;
    width: 4px;
    height: 4px;
    background-color: #333;
    top: 3px;
}

.player .eye-left {
    left: 5px;
}

.player .eye-right {
    right: 5px;
}

/* 嘴巴 */
.player .mouth {
    position: absolute;
    width: 8px;
    height: 2px;
    background-color: #333;
    bottom: 2px;
    left: 8px;
}

.game-info {
    margin-top: 20px;
    text-align: center;
    width: 100%;
}

.controls-info {
    display: flex;
    justify-content: space-around;
    margin-top: 15px;
}

.player-controls {
    background-color: #444;
    padding: 10px 20px;
    border-radius: 5px;
    margin: 0 10px;
}

.player-controls h2 {
    margin-bottom: 5px;
    font-size: 18px;
}

/* 添加一些像素风格的方块作为装饰 */
#game-world::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 32px;
    background-color: #5E4C3E; /* 泥土颜色 */
    background-image: linear-gradient(#5E4C3E 8px, #4A3C2E 8px);
    background-size: 32px 16px;
    z-index: 1;
}

/* 卡片样式 */
.card {
    box-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    transform-style: preserve-3d;
    backface-visibility: hidden;
}

/* 玩家状态显示 */
.player-status {
    position: absolute;
    background-color: rgba(0, 0, 0, 0.7);
    border-radius: 5px;
    padding: 8px;
    color: white;
    font-size: 14px;
    z-index: 10;
    width: 150px;
}

#player1-status {
    top: 10px;
    left: 10px;
    border: 2px solid #F44336;
}

#player2-status {
    top: 10px;
    right: 10px;
    border: 2px solid #2196F3;
}

.player-name {
    font-weight: bold;
    margin-bottom: 5px;
    text-align: center;
}

.health-bar {
    height: 15px;
    background-color: #333;
    border-radius: 3px;
    margin-bottom: 5px;
    overflow: hidden;
    position: relative;
}

.health-fill {
    height: 100%;
    background-color: #4CAF50;
    width: 100%;
    transition: width 0.3s, background-color 0.3s;
}

.health-text {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 10px;
    font-weight: bold;
    text-shadow: 1px 1px 1px rgba(0,0,0,0.7);
}

.wealth {
    text-align: right;
    font-size: 12px;
}

/* 浮动文本动画 */
@keyframes shake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
    20%, 40%, 60%, 80% { transform: translateX(5px); }
}

/* 发射器样式 */
.launcher {
    position: absolute;
    width: 18px;
    height: 8px;
    background-color: #555;
    border: 1px solid #333;
    z-index: 11;
    border-radius: 2px 4px 4px 2px;
    /* 枪身结构 */
    position: relative;
    /* 添加外星科技感纹理 */
    background-image: linear-gradient(90deg, transparent 50%, rgba(255, 255, 255, 0.1) 50%);
    background-size: 4px 4px;
}

/* 添加枪柄 */
.launcher::before {
    content: '';
    position: absolute;
    width: 6px;
    height: 10px;
    background-color: inherit;
    border: 1px solid;
    border-color: inherit;
    bottom: -8px;
    left: 2px;
    border-radius: 1px 1px 2px 2px;
    transform: rotate(10deg);
}

/* 添加枪口 */
.launcher::after {
    content: '';
    position: absolute;
    width: 4px;
    height: 4px;
    background-color: #222;
    right: -2px;
    top: 2px;
    border-radius: 50%;
}

.red-player .launcher {
    background-color: #AA3333;
    border-color: #882222;
    transform: rotate(-5deg);
    /* 红色玩家的外星枪特有纹理 */
    background-image: linear-gradient(90deg, transparent 50%, rgba(255, 255, 255, 0.15) 50%);
    box-shadow: 0 0 3px rgba(255, 0, 0, 0.3);
}

.red-player .launcher::before {
    /* 红色玩家的枪柄特征 */
    height: 12px;
    bottom: -10px;
    transform: rotate(15deg);
}

.red-player .launcher::after {
    background-color: #FF5555;
    box-shadow: 0 0 3px #FF5555;
    width: 5px;
    height: 5px;
}

.blue-player .launcher {
    background-color: #3333AA;
    border-color: #222288;
    transform: rotate(-5deg);
    /* 蓝色玩家的外星枪特有纹理 */
    background-image: linear-gradient(45deg, transparent 50%, rgba(255, 255, 255, 0.15) 50%);
    box-shadow: 0 0 3px rgba(0, 0, 255, 0.3);
}

.blue-player .launcher::before {
    /* 蓝色玩家的枪柄特征 */
    height: 11px;
    bottom: -9px;
    transform: rotate(12deg);
    border-radius: 2px;
}

.blue-player .launcher::after {
    background-color: #5555FF;
    box-shadow: 0 0 3px #5555FF;
    width: 4px;
    height: 4px;
    right: -3px;
}

/* 玩家死亡状态 */
.player.dead {
    transform: rotate(90deg) scale(0.8); /* 旋转并稍微缩小 */
    opacity: 0.6;
    transition: transform 0.5s ease-out, opacity 0.5s ease-out;
}

/* 灵魂效果 */
.soul {
    position: absolute;
    width: 20px;
    height: 20px;
    background-color: rgba(230, 230, 250, 0.7); /* 淡紫色半透明 */
    border-radius: 50%;
    box-shadow: 0 0 15px rgba(230, 230, 250, 0.9);
    z-index: 15; /* 确保在玩家上方 */
    pointer-events: none; /* 不阻挡鼠标事件 */
    animation: soulRise 5s linear forwards;
}

@keyframes soulRise {
    0% {
        transform: translateY(0) scale(1);
        opacity: 0.7;
    }
    100% {
        transform: translateY(-200px) scale(0.5); /* 向上移动并缩小 */
        opacity: 0;
    }
}

/* 火球样式 */
.fireball {
    border-radius: 50%;
    animation: fireball-pulse 0.5s infinite alternate;
    position: absolute;
    z-index: 8;
    transform-origin: center;
}

/* 火球脉动动画 */
@keyframes fireball-pulse {
    0% { transform: scale(0.9); opacity: 0.8; }
    100% { transform: scale(1.1); opacity: 1; }
}