// 游戏常量
const GAME_WIDTH = 1200;
const GAME_HEIGHT = 800;
const PLAYER_WIDTH = 32;
const PLAYER_HEIGHT = 48;
const MOVE_SPEED = 5;
const FIREBALL_WIDTH = 16;
const FIREBALL_HEIGHT = 16;
const FIREBALL_SPEED = 8;

// 获取DOM元素
const player1 = document.getElementById('player1');
const player2 = document.getElementById('player2');
const gameWorld = document.getElementById('game-world');

// 玩家状态 - 设置为window对象的属性，使其可以被其他脚本访问
window.player1State = {
    x: 100,
    y: 100,
    direction: 'right',
    isMoving: false,
    health: 100, // 添加血量属性
    wealth: 0,   // 添加财富值属性
    canShoot: true, // 是否可以发射火球（冷却控制）
    isFrozen: false, // 是否被冰冻（无法移动）
    isDead: false, // 新增：是否死亡
    soulElement: null, // 新增：灵魂元素引用
    keys: {
        up: false,
        down: false,
        left: false,
        right: false,
        shoot: false // 发射火球按键
    },
    isEnlarged: false, // 是否处于变大状态
    isInvincible: false, // 是否处于无敌状态
    originalWidth: PLAYER_WIDTH,
    originalHeight: PLAYER_HEIGHT,
    enlargeTimeoutId: null // 变大效果的计时器ID
};

window.player2State = {
    x: 200,
    y: 100,
    direction: 'right',
    isMoving: false,
    health: 100, // 添加血量属性
    wealth: 0,   // 添加财富值属性
    canShoot: true, // 是否可以发射火球（冷却控制）
    isFrozen: false, // 是否被冰冻（无法移动）
    isDead: false, // 新增：是否死亡
    soulElement: null, // 新增：灵魂元素引用
    keys: {
        up: false,
        down: false,
        left: false,
        right: false,
        shoot: false // 发射火球按键
    },
    isEnlarged: false, // 是否处于变大状态
    isInvincible: false, // 是否处于无敌状态
    originalWidth: PLAYER_WIDTH,
    originalHeight: PLAYER_HEIGHT,
    enlargeTimeoutId: null // 变大效果的计时器ID
};

// 火球数组，用于跟踪所有火球
let fireballs = [];

// 初始化游戏
function initGame() {
    // 设置初始位置
    updatePlayerPosition(player1, player1State);
    updatePlayerPosition(player2, player2State);
    
    // 添加随机功能卡片
    addCards();
    
    // 创建玩家状态显示
    createPlayerStatusDisplay();
    
    
    // 开始游戏循环
    gameLoop();
}

// 添加装饰方块
// 卡片类型枚举
const CardType = {
    FOOD: 'food',           // 食物卡（增加血量）
    MOVEMENT: 'movement',   // 位置移动卡（前后左右）
    MONSTER: 'monster',     // 怪兽卡（造成伤害）
    TREASURE: 'treasure',   // 财富卡（增加财富值）
    BOSS: 'boss',           // Boss卡（造成致命伤害）
    ICE_BALL: 'ice_ball'    // 冰弹卡（冰冻效果）
};

// 卡片数组，用于跟踪所有卡片
let cards = [];

// 创建单个卡片的函数
function createCard() {
    const cardBackColor = '#8B4513'; // 卡片背面颜色
    
    // 创建卡片元素
    const card = document.createElement('div');
    card.className = 'card';
    card.style.width = '48px'; // 增大卡片尺寸
    card.style.height = '48px'; // 增大卡片尺寸
    card.style.position = 'absolute';
    card.style.backgroundColor = cardBackColor;
    card.style.left = Math.floor(Math.random() * (GAME_WIDTH - 48)) + 'px'; // 调整随机位置计算
    card.style.top = Math.floor(Math.random() * (GAME_HEIGHT - 80)) + 'px'; // 调整随机位置计算
    card.style.zIndex = '5';
    card.style.border = '3px solid rgba(0,0,0,0.3)'; // 增大边框
    card.style.borderRadius = '6px'; // 调整圆角
    card.style.transition = 'transform 0.3s, background-color 0.3s';
    card.style.cursor = 'pointer';
    
    // 随机分配卡片类型
    const cardTypes = [CardType.FOOD, CardType.MOVEMENT, CardType.MONSTER, CardType.TREASURE];
    
    // 有10%的概率生成BOSS卡片，有15%的概率生成冰弹卡片
    const isBoss = Math.random() < 0.1;
    const isIceBall = !isBoss && Math.random() < 0.15;
    const cardType = isBoss ? CardType.BOSS : (isIceBall ? CardType.ICE_BALL : cardTypes[Math.floor(Math.random() * cardTypes.length)]);
    
    // 存储卡片数据
    const cardData = {
        element: card,
        type: cardType,
        revealed: false,
        active: true,
        x: parseInt(card.style.left),
        y: parseInt(card.style.top)
    };
    
    // 将卡片添加到数组中
    cards.push(cardData);
    
    // 添加到游戏世界
    gameWorld.appendChild(card);
    
    return cardData;
}

function addCards() {
    // 添加随机功能卡片
    for (let i = 0; i < 15; i++) {
        createCard();
    }
}

// 更新玩家位置
function updatePlayerPosition(playerElement, playerState) {
    playerElement.style.left = playerState.x + 'px';
    playerElement.style.top = playerState.y + 'px';
    
    // 根据方向和变大状态设置 transform
    let scaleValue = playerState.isEnlarged ? 'scale(2)' : 'scale(1)';
    let scaleXValue = playerState.direction === 'left' ? 'scaleX(-1)' : 'scaleX(1)';
    playerElement.style.transform = `${scaleXValue} ${scaleValue}`; // 组合 transform
    
    // 更新发射器位置
    updateLauncherPosition(playerElement, playerState);
    
    // 更新动画状态
    updatePlayerAnimation(playerElement, playerState);
}

// 更新发射器位置
function updateLauncherPosition(playerElement, playerState) {
    // 获取发射器元素
    const launcherId = playerElement.id === 'player1' ? 'launcher1' : 'launcher2';
    const launcher = document.getElementById(launcherId);
    
    if (launcher) {
        // 根据玩家方向设置发射器位置
        if (playerState.direction === 'right') {
            launcher.style.left = '24px'; // 发射器位于玩家右侧
            launcher.style.top = '18px'; // 发射器位于玩家手臂位置
            launcher.style.transform = 'rotate(0deg)'; // 朝右的角度
        } else {
            launcher.style.left = '-12px'; // 发射器位于玩家左侧
            launcher.style.top = '18px'; // 发射器位于玩家手臂位置
            launcher.style.transform = 'rotate(180deg)'; // 朝左的角度
        }
    }
}

// 处理玩家移动
function movePlayer(playerState, playerElement) {
    // 如果玩家死亡或被冰冻，则无法移动
    if (playerState.isDead || playerState.isFrozen) {
        playerState.isMoving = false;
        updatePlayerAnimation(playerElement, playerState); // 确保停止移动动画
        return false;
    }

    let moved = false;
    let wasMoving = playerState.isMoving;
    
    if (playerState.keys.up && playerState.y > 0) {
        playerState.y -= MOVE_SPEED;
        moved = true;
    }
    if (playerState.keys.down && playerState.y < GAME_HEIGHT - PLAYER_HEIGHT) {
        playerState.y += MOVE_SPEED;
        moved = true;
    }
    if (playerState.keys.left && playerState.x > 0) {
        playerState.x -= MOVE_SPEED;
        playerState.direction = 'left';
        moved = true;
    }
    if (playerState.keys.right && playerState.x < GAME_WIDTH - PLAYER_WIDTH) {
        playerState.x += MOVE_SPEED;
        playerState.direction = 'right';
        moved = true;
    }
    
    // 更新移动状态
    playerState.isMoving = moved;
    
    if (moved) {
        updatePlayerPosition(playerElement, playerState);
    }
    
    return moved;
}

// 游戏循环
function gameLoop() {
    // 移动玩家
    const player1Moved = movePlayer(player1State, player1);
    const player2Moved = movePlayer(player2State, player2);
    
    // 处理玩家发射火球
    handlePlayerShooting(player1State, player1, 1);
    handlePlayerShooting(player2State, player2, 2);
    
    // 更新火球位置并检测碰撞
    updateFireballs();
    
    // 检测玩家之间的碰撞
    checkCollision();
    
    // 检测玩家与卡片的碰撞
    checkCardCollisions();
    
    // 更新玩家状态显示
    updatePlayerStatusDisplay();
    
    // 继续游戏循环
    requestAnimationFrame(gameLoop);
}

// 检测玩家之间的碰撞
function checkCollision() {
    // 获取玩家的边界框
    const player1Rect = player1.getBoundingClientRect();
    const player2Rect = player2.getBoundingClientRect();
    
    // 检查玩家之间的碰撞
    if (isColliding(player1Rect, player2Rect)) {
        // 播放碰撞音效
        if (typeof soundGenerator !== 'undefined') {
            console.log('Playing hit sound - player collision');
            soundGenerator.generateHitSound();
        } else {
            console.log('soundGenerator is undefined');
        }
        
        // 简单的碰撞响应 - 将两个玩家稍微分开
        const dx = player1State.x - player2State.x;
        const dy = player1State.y - player2State.y;
        
        if (Math.abs(dx) > Math.abs(dy)) {
            // 水平碰撞
            if (dx > 0) {
                player1State.x += 2;
                player2State.x -= 2;
            } else {
                player1State.x -= 2;
                player2State.x += 2;
            }
        } else {
            // 垂直碰撞
            if (dy > 0) {
                player1State.y += 2;
                player2State.y -= 2;
            } else {
                player1State.y -= 2;
                player2State.y += 2;
            }
        }
        
        // 更新位置
        updatePlayerPosition(player1, player1State);
        updatePlayerPosition(player2, player2State);
    }
}

// 检测玩家与卡片的碰撞
function checkCardCollisions() {
    // 获取玩家的边界框
    const player1Rect = player1.getBoundingClientRect();
    const player2Rect = player2.getBoundingClientRect();
    
    // 检查每张卡片
    for (let i = 0; i < cards.length; i++) {
        const card = cards[i];
        
        // 如果卡片已经不活跃（已被使用），则跳过
        if (!card.active) continue;
        
        const cardRect = card.element.getBoundingClientRect();
        
        // 检查玩家1与卡片的碰撞
        if (isColliding(player1Rect, cardRect)) {
            revealCard(card, player1State, player1);
        }
        
        // 检查玩家2与卡片的碰撞
        if (isColliding(player2Rect, cardRect)) {
            revealCard(card, player2State, player2);
        }
    }
}

// 翻转并激活卡片效果
function revealCard(card, playerState, playerElement) {
    // 如果卡片已经被翻转，则跳过
    if (card.revealed) return;
    
    // 标记卡片为已翻转
    card.revealed = true;
    
    
    // 根据卡片类型设置颜色和图片
    let cardColor;
    let cardImage;
    switch (card.type) {
        case CardType.FOOD:
            cardColor = '#8BC34A'; // 绿色
            cardImage = 'images/food.svg';
            break;
        case CardType.MOVEMENT:
            cardColor = '#2196F3'; // 蓝色
            cardImage = 'images/movement.svg';
            break;
        case CardType.MONSTER:
            cardColor = '#F44336'; // 红色
            cardImage = 'images/monster.svg';
            break;
        case CardType.TREASURE:
            cardColor = '#FFD700'; // 金色
            cardImage = 'images/treasure.svg';
            break;
        case CardType.BOSS: // 现在是红色药丸卡
            cardColor = '#FF6B6B'; // 红色
            cardImage = 'images/red_pill.svg';
            break;
        case CardType.ICE_BALL:
            cardColor = '#00BFFF'; // 冰蓝色
            cardImage = 'images/ice_ball.svg';
            break;
    }
    
    // 播放卡片翻转音效
    if (typeof soundGenerator !== 'undefined') {
        soundGenerator.generateCardFlipSound();
    }
    
    // 翻转动画
    card.element.style.transform = 'rotateY(180deg)';
    
    // 延迟更改背景色和添加图片，与翻转动画同步
    setTimeout(() => {
        card.element.style.backgroundColor = cardColor;
        
        // 确保图片正确加载
        const img = new Image();
        img.onload = function() {
            // 图片加载成功后设置背景
            card.element.style.backgroundImage = `url(${cardImage})`;
            card.element.style.backgroundSize = 'cover';
            card.element.style.backgroundPosition = 'center';
        };
        img.onerror = function() {
            console.error(`无法加载图片: ${cardImage}`);
            // 加载失败时使用纯色背景
            card.element.style.backgroundImage = 'none';
        };
        img.src = cardImage;
        
        card.element.style.transform = 'rotateY(0deg)';
        
        // 应用卡片效果
        applyCardEffect(card, playerState, playerElement);
        
        // 2秒后移除卡片并创建新卡片
        setTimeout(() => {
            card.active = false;
            card.element.style.opacity = '0';
            setTimeout(() => {
                card.element.remove();
                // 创建一个新卡片来替代消失的卡片
                createCard();
            }, 300);
        }, 2000);
    }, 150);
}

// 应用卡片效果
function applyCardEffect(card, playerState, playerElement) {
    
    switch (card.type) {
        case CardType.FOOD:
            // 食物卡 - 增加血量
            const healthBoost = Math.floor(Math.random() * 20) + 10; // 10-30点血量
            playerState.health = Math.min(playerState.health + healthBoost, 100);
            showFloatingText('+' + healthBoost + ' 血量', card.x, card.y, '#8BC34A');
            
            // 播放收集音效
            if (typeof soundGenerator !== 'undefined') {
                soundGenerator.generateCollectSound();
            }
            break;
            
        case CardType.MOVEMENT:
            // 位置移动卡 - 随机移动玩家
            const directions = ['up', 'down', 'left', 'right'];
            const direction = directions[Math.floor(Math.random() * directions.length)];
            const moveAmount = 100; // 移动距离
            
            switch (direction) {
                case 'up':
                    playerState.y = Math.max(0, playerState.y - moveAmount);
                    showFloatingText('向上移动!', card.x, card.y, '#2196F3');
                    break;
                case 'down':
                    playerState.y = Math.min(GAME_HEIGHT - PLAYER_HEIGHT, playerState.y + moveAmount);
                    showFloatingText('向下移动!', card.x, card.y, '#2196F3');
                    break;
                case 'left':
                    playerState.x = Math.max(0, playerState.x - moveAmount);
                    playerState.direction = 'left';
                    showFloatingText('向左移动!', card.x, card.y, '#2196F3');
                    break;
                case 'right':
                    playerState.x = Math.min(GAME_WIDTH - PLAYER_WIDTH, playerState.x + moveAmount);
                    playerState.direction = 'right';
                    showFloatingText('向右移动!', card.x, card.y, '#2196F3');
                    break;
            }
            
            updatePlayerPosition(playerElement, playerState);
            break;
            
        case CardType.MONSTER:
            // 怪兽卡 - 造成伤害
            const damage = Math.floor(Math.random() * 15) + 5; // 5-20点伤害
            playerState.health = Math.max(0, playerState.health - damage);
            showFloatingText('-' + damage + ' 伤害', card.x, card.y, '#F44336');
            
            // 播放伤害音效
            if (typeof soundGenerator !== 'undefined') {
                soundGenerator.generateDamageSound();
            }
            
            // 震动效果
            playerElement.style.animation = 'shake 0.5s';
            setTimeout(() => {
                playerElement.style.animation = '';
            }, 500);
            break;
            
        case CardType.TREASURE:
            // 财富卡 - 增加财富值
            const wealthGain = Math.floor(Math.random() * 30) + 10; // 10-40点财富
            playerState.wealth += wealthGain;
            showFloatingText('+' + wealthGain + ' 财富', card.x, card.y, '#FFD700');
            
            // 播放收集音效
            if (typeof soundGenerator !== 'undefined') {
                soundGenerator.generateCollectSound();
            }
            break;
            
        case CardType.BOSS: // 红色药丸效果
            showFloatingText('变大 + 无敌!', card.x, card.y, '#FF6B6B');
            
            // 播放无敌音效
            if (typeof soundGenerator !== 'undefined') {
                soundGenerator.generateInvincibleSound();
            }

            // 清除任何现有的变大/无敌计时器
            if (playerState.enlargeTimeoutId) {
                clearTimeout(playerState.enlargeTimeoutId);
                playerState.enlargeTimeoutId = null; // 清除计时器ID引用
            }

            // 首次应用或刷新效果时，添加类和设置状态
            // 使用 classList.add 是幂等的，重复添加无副作用
            playerElement.classList.add('enlarged', 'invincible');
            playerState.isEnlarged = true;
            playerState.isInvincible = true; // 设置无敌状态
            playerElement.style.zIndex = '100'; // 确保在最前面

            // 设置新的10秒计时器来恢复状态
            playerState.enlargeTimeoutId = setTimeout(() => {
                playerElement.classList.remove('enlarged', 'invincible');
                playerElement.style.zIndex = '10'; // 恢复层级
                playerState.isEnlarged = false;
                playerState.isInvincible = false; // 取消无敌状态
                // 检查元素是否存在，避免在玩家被销毁后尝试访问offsetWidth
                if (document.body.contains(playerElement)) {
                    showFloatingText('效果消失', playerState.x + playerElement.offsetWidth / 2, playerState.y, '#FF6B6B');
                }
                playerState.enlargeTimeoutId = null; // 清除计时器ID
            }, 10000); // 10秒

            break; // 确保 break 在这里
            
        case CardType.ICE_BALL:
            // 冰弹卡 - 给予玩家冰冻能力
            showFloatingText('获得冰弹能力!', card.x, card.y, '#00BFFF');
            
            // 播放冰冻音效
            if (typeof soundGenerator !== 'undefined') {
                soundGenerator.generateFreezeSound();
            }
            
            // 添加冰冻效果
            playerElement.style.boxShadow = '0 0 15px #00BFFF';
            playerElement.style.border = '2px solid #00BFFF';
            
            // 冰弹效果持续10秒
            playerState.hasIceBall = true;
            
            setTimeout(() => {
                // 10秒后移除冰弹效果
                playerState.hasIceBall = false;
                playerElement.style.boxShadow = '';
                playerElement.style.border = '';
                showFloatingText('冰弹能力消失', playerState.x, playerState.y, '#00BFFF');
            }, 10000);
            break;
    }
}

// 显示浮动文本效果
function showFloatingText(text, x, y, color) {
    const floatingText = document.createElement('div');
    floatingText.className = 'floating-text';
    floatingText.textContent = text;
    floatingText.style.position = 'absolute';
    floatingText.style.left = x + 'px';
    floatingText.style.top = y + 'px';
    floatingText.style.color = color;
    floatingText.style.fontWeight = 'bold';
    floatingText.style.fontSize = '16px';
    floatingText.style.textShadow = '1px 1px 2px rgba(0,0,0,0.7)';
    floatingText.style.zIndex = '10';
    floatingText.style.pointerEvents = 'none';
    floatingText.style.transition = 'transform 1s, opacity 1s';
    floatingText.style.opacity = '1';
    
    gameWorld.appendChild(floatingText);
    
    // 动画效果
    setTimeout(() => {
        floatingText.style.transform = 'translateY(-30px)';
        floatingText.style.opacity = '0';
        
        // 移除元素
        setTimeout(() => {
            floatingText.remove();
        }, 1000);
    }, 10);
}

// 创建玩家状态显示
function createPlayerStatusDisplay() {
    // 创建玩家1状态显示
    const player1Status = document.createElement('div');
    player1Status.id = 'player1-status';
    player1Status.className = 'player-status';
    player1Status.innerHTML = `
        <div class="player-name">红色玩家 (1P)</div>
        <div class="health-bar">
            <div class="health-fill" id="player1-health"></div>
            <div class="health-text" id="player1-health-text">100</div>
        </div>
        <div class="wealth">财富: <span id="player1-wealth">0</span></div>
    `;
    
    // 创建玩家2状态显示
    const player2Status = document.createElement('div');
    player2Status.id = 'player2-status';
    player2Status.className = 'player-status';
    player2Status.innerHTML = `
        <div class="player-name">蓝色玩家 (2P)</div>
        <div class="health-bar">
            <div class="health-fill" id="player2-health"></div>
            <div class="health-text" id="player2-health-text">100</div>
        </div>
        <div class="wealth">财富: <span id="player2-wealth">0</span></div>
    `;
    
    // 添加到游戏世界
    gameWorld.appendChild(player1Status);
    gameWorld.appendChild(player2Status);
    
    // 初始更新状态显示
    updatePlayerStatusDisplay();
}

// 更新玩家状态显示
function updatePlayerStatusDisplay() {
    // 更新玩家1状态
    const player1Health = document.getElementById('player1-health');
    const player1HealthText = document.getElementById('player1-health-text');
    const player1Wealth = document.getElementById('player1-wealth');
    
    player1Health.style.width = player1State.health + '%';
    player1HealthText.textContent = Math.round(player1State.health);
    player1Wealth.textContent = player1State.wealth;
    
    // 根据血量改变颜色
    if (player1State.health > 60) {
        player1Health.style.backgroundColor = '#4CAF50'; // 绿色
    } else if (player1State.health > 30) {
        player1Health.style.backgroundColor = '#FFC107'; // 黄色
    } else {
        player1Health.style.backgroundColor = '#F44336'; // 红色
    }
    
    // 更新玩家2状态
    const player2Health = document.getElementById('player2-health');
    const player2HealthText = document.getElementById('player2-health-text');
    const player2Wealth = document.getElementById('player2-wealth');
    
    player2Health.style.width = player2State.health + '%';
    player2HealthText.textContent = Math.round(player2State.health);
    player2Wealth.textContent = player2State.wealth;
    
    // 根据血量改变颜色
    if (player2State.health > 60) {
        player2Health.style.backgroundColor = '#4CAF50'; // 绿色
    } else if (player2State.health > 30) {
        player2Health.style.backgroundColor = '#FFC107'; // 黄色
    } else {
        player2Health.style.backgroundColor = '#F44336'; // 红色
    }

    // 检查玩家1是否死亡
    if (player1State.health <= 0 && !player1State.isDead) {
        handlePlayerDeath(player1State, player1);
    }

    // 检查玩家2是否死亡
    if (player2State.health <= 0 && !player2State.isDead) {
        handlePlayerDeath(player2State, player2);
    }
}

// 处理玩家死亡
function handlePlayerDeath(playerState, playerElement) {
    playerState.isDead = true;
    playerElement.style.display = 'none'; // 隐藏玩家元素
    
    // 播放死亡音效
    if (typeof soundGenerator !== 'undefined') {
        soundGenerator.generateDeathSound();
    }

    // 创建灵魂元素
    const soul = document.createElement('div');
    soul.className = 'soul';
    soul.style.position = 'absolute';
    soul.style.width = PLAYER_WIDTH + 'px';
    soul.style.height = PLAYER_HEIGHT + 'px';
    soul.style.left = playerState.x + 'px';
    soul.style.top = playerState.y + 'px';
    soul.style.backgroundImage = 'url(images/soul.svg)'; // 假设有一个灵魂的SVG图片
    soul.style.backgroundSize = 'cover';
    soul.style.zIndex = '10';
    soul.style.transition = 'transform 5s ease-out, opacity 5s ease-out';
    gameWorld.appendChild(soul);
    playerState.soulElement = soul; // 保存灵魂元素的引用

    // 灵魂飘走动画
    setTimeout(() => {
        soul.style.transform = 'translateY(-100px) scale(0.5)';
        soul.style.opacity = '0';
    }, 10);

    // 5秒后复活
    setTimeout(() => {
        // 移除灵魂元素
        if (playerState.soulElement) {
            playerState.soulElement.remove();
            playerState.soulElement = null;
        }

        // 重置玩家状态并复活
        playerState.health = 100; // 恢复血量
        playerState.isDead = false;
        playerState.isFrozen = false; // 确保不是冰冻状态
        playerState.isInvincible = false; // 确保不是无敌状态
        playerElement.style.display = 'block'; // 显示玩家元素
        // 可以选择将玩家移动到起始位置或随机位置
        // playerState.x = playerElement.id === 'player1' ? 100 : 200;
        // playerState.y = 100;
        // updatePlayerPosition(playerElement, playerState);
        
        // 播放复活音效
        if (typeof soundGenerator !== 'undefined') {
            soundGenerator.generateResurrectSound();
        }
        
        showFloatingText('复活!', playerState.x + playerElement.offsetWidth / 2, playerState.y, '#00FF00');
    }, 5000); // 5000毫秒 = 5秒
}

// 碰撞检测辅助函数
function isColliding(rect1, rect2) {
    return (
        rect1.left < rect2.right &&
        rect1.right > rect2.left &&
        rect1.top < rect2.bottom &&
        rect1.bottom > rect2.top
    );
}

// 键盘事件监听
document.addEventListener('keydown', (e) => {
    handleKeyEvent(e, true);
});

document.addEventListener('keyup', (e) => {
    handleKeyEvent(e, false);
});

// 处理键盘事件
function handleKeyEvent(e, isKeyDown) {
    // 防止按键导致页面滚动
    if (['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight', 'w', 'a', 's', 'd', 'W', 'A', 'S', 'D', ' ', '0'].includes(e.key)) {
        e.preventDefault();
    }
    
    // 玩家1 - 方向键和数字0键发射火球
    switch (e.key) {
        case 'ArrowUp':
            player1State.keys.up = isKeyDown;
            break;
        case 'ArrowDown':
            player1State.keys.down = isKeyDown;
            break;
        case 'ArrowLeft':
            player1State.keys.left = isKeyDown;
            break;
        case 'ArrowRight':
            player1State.keys.right = isKeyDown;
            break;
        case '0': // 数字0键发射火球
            player1State.keys.shoot = isKeyDown;
            break;
    }
    
    // 玩家2 - WASD和空格键发射火球
    switch (e.key.toLowerCase()) {
        case 'w':
            player2State.keys.up = isKeyDown;
            break;
        case 's':
            player2State.keys.down = isKeyDown;
            break;
        case 'a':
            player2State.keys.left = isKeyDown;
            break;
        case 'd':
            player2State.keys.right = isKeyDown;
            break;
        case ' ': // 空格键发射火球
            player2State.keys.shoot = isKeyDown;
            break;
    }
}

// 更新玩家动画
function updatePlayerAnimation(playerElement, playerState) {
    // 获取玩家的手臂和腿部元素
    const armLeft = playerElement.querySelector('.arm-left');
    const armRight = playerElement.querySelector('.arm-right');
    const legLeft = playerElement.querySelector('.leg-left');
    const legRight = playerElement.querySelector('.leg-right');
    
    // 如果玩家正在移动，添加摆动动画
    if (playerState.isMoving) {
        // 使用时间来创建摆动效果
        const time = Date.now() / 150;
        const swingAngle = Math.sin(time) * 20; // 摆动角度
        
        // 手臂摆动
        armLeft.style.transform = `rotate(${-swingAngle}deg)`;
        armRight.style.transform = `rotate(${swingAngle}deg)`;
        
        // 腿部摆动
        legLeft.style.transform = `rotate(${swingAngle}deg)`;
        legRight.style.transform = `rotate(${-swingAngle}deg)`;
    } else {
        // 如果不移动，恢复默认姿势
        armLeft.style.transform = 'rotate(0deg)';
        armRight.style.transform = 'rotate(0deg)';
        legLeft.style.transform = 'rotate(0deg)';
        legRight.style.transform = 'rotate(0deg)';
    }
}

// 创建火球元素
function createFireball(playerState, playerId) {
    // 创建火球元素
    const fireball = document.createElement('div');
    fireball.className = 'fireball';
    
    // 设置火球样式
    let currentFireballWidth = FIREBALL_WIDTH;
    let currentFireballHeight = FIREBALL_HEIGHT;

    // 如果玩家处于变大状态，火球也变大
    if (playerState.isEnlarged) {
        currentFireballWidth *= 4;
        currentFireballHeight *= 4;
    }

    fireball.style.width = currentFireballWidth + 'px';
    fireball.style.height = currentFireballHeight + 'px';
    fireball.style.position = 'absolute';
    fireball.style.borderRadius = '50%';
    fireball.style.zIndex = '8';
    
    // 根据玩家设置火球颜色
    if (playerId === 1) {
        fireball.style.backgroundColor = '#FF7F50'; // 红色玩家的火球颜色
        fireball.style.boxShadow = '0 0 10px #FF4500';
    } else {
        fireball.style.backgroundColor = '#4F94CD'; // 蓝色玩家的火球颜色
        fireball.style.boxShadow = '0 0 10px #1E90FF';
    }
    
    // 获取发射器元素
    const launcherId = playerId === 1 ? 'launcher1' : 'launcher2';
    const launcher = document.getElementById(launcherId);
    const playerElement = playerId === 1 ? player1 : player2;
    
    // 计算火球初始位置（从发射器位置发射）
    let fireballX, fireballY;
    
    if (launcher) {
        // 获取发射器的位置信息
        const playerRect = playerElement.getBoundingClientRect();
        const launcherRect = launcher.getBoundingClientRect();
        
        // 计算发射器相对于游戏世界的位置
        const gameWorldRect = gameWorld.getBoundingClientRect();
        const launcherAbsX = launcherRect.left - gameWorldRect.left;
        const launcherAbsY = launcherRect.top - gameWorldRect.top;
        
        // 根据玩家方向设置火球位置
        if (playerState.direction === 'right') {
            fireballX = launcherAbsX + launcher.offsetWidth;
            fireballY = launcherAbsY + launcher.offsetHeight / 2 - FIREBALL_HEIGHT / 2;
        } else {
            fireballX = launcherAbsX - FIREBALL_WIDTH;
            fireballY = launcherAbsY + launcher.offsetHeight / 2 - FIREBALL_HEIGHT / 2;
        }
    } else {
        // 如果找不到发射器，则使用玩家位置作为备用
        if (playerState.direction === 'right') {
            fireballX = playerState.x + PLAYER_WIDTH;
            fireballY = playerState.y + PLAYER_HEIGHT / 2 - FIREBALL_HEIGHT / 2;
        } else {
            fireballX = playerState.x - FIREBALL_WIDTH;
            fireballY = playerState.y + PLAYER_HEIGHT / 2 - FIREBALL_HEIGHT / 2;
        }
    }
    
    // 设置火球位置
    fireball.style.left = fireballX + 'px';
    fireball.style.top = fireballY + 'px';
    
    // 添加到游戏世界
    gameWorld.appendChild(fireball);
    
    // 播放发射音效
    if (window.AudioManager) {
        AudioManager.playSound('movement'); // 使用现有的音效
    }
    
    // 返回火球数据
    return {
        element: fireball,
        x: fireballX,
        y: fireballY,
        direction: playerState.direction,
        playerId: playerId, // 记录是哪个玩家发射的
        active: true,
        isLarge: playerState.isEnlarged // 标记是否为大火球
    };
}

// 创建冰球元素
function createIceBall(playerState, playerId) {
    // 创建冰球元素
    const iceball = document.createElement('div');
    iceball.className = 'iceball';
    
    // 设置冰球样式
    iceball.style.width = FIREBALL_WIDTH + 'px';
    iceball.style.height = FIREBALL_HEIGHT + 'px';
    iceball.style.position = 'absolute';
    iceball.style.borderRadius = '50%';
    iceball.style.zIndex = '8';
    
    // 设置冰球颜色和效果
    iceball.style.backgroundColor = '#00BFFF'; // 冰蓝色
    iceball.style.boxShadow = '0 0 10px #87CEFA';
    
    // 获取发射器元素
    const launcherId = playerId === 1 ? 'launcher1' : 'launcher2';
    const launcher = document.getElementById(launcherId);
    const playerElement = playerId === 1 ? player1 : player2;
    
    // 计算冰球初始位置（从发射器位置发射）
    let iceballX, iceballY;
    
    if (launcher) {
        // 获取发射器的位置信息
        const playerRect = playerElement.getBoundingClientRect();
        const launcherRect = launcher.getBoundingClientRect();
        
        // 计算发射器相对于游戏世界的位置
        const gameWorldRect = gameWorld.getBoundingClientRect();
        const launcherAbsX = launcherRect.left - gameWorldRect.left;
        const launcherAbsY = launcherRect.top - gameWorldRect.top;
        
        // 根据玩家方向设置冰球位置
        if (playerState.direction === 'right') {
            iceballX = launcherAbsX + launcher.offsetWidth;
            iceballY = launcherAbsY + launcher.offsetHeight / 2 - FIREBALL_HEIGHT / 2;
        } else {
            iceballX = launcherAbsX - FIREBALL_WIDTH;
            iceballY = launcherAbsY + launcher.offsetHeight / 2 - FIREBALL_HEIGHT / 2;
        }
    } else {
        // 如果找不到发射器，则使用玩家位置作为备用
        if (playerState.direction === 'right') {
            iceballX = playerState.x + PLAYER_WIDTH;
            iceballY = playerState.y + PLAYER_HEIGHT / 2 - FIREBALL_HEIGHT / 2;
        } else {
            iceballX = playerState.x - FIREBALL_WIDTH;
            iceballY = playerState.y + PLAYER_HEIGHT / 2 - FIREBALL_HEIGHT / 2;
        }
    }
    
    // 设置冰球位置
    iceball.style.left = iceballX + 'px';
    iceball.style.top = iceballY + 'px';
    
    // 添加到游戏世界
    gameWorld.appendChild(iceball);
    
    // 播放发射音效
    if (window.AudioManager) {
        AudioManager.playSound('movement'); // 使用现有的音效
    }
    
    // 返回冰球数据
    return {
        element: iceball,
        x: iceballX,
        y: iceballY,
        direction: playerState.direction,
        playerId: playerId, // 记录是哪个玩家发射的
        active: true,
        isIceBall: true // 标记为冰球
    };
}

// 处理玩家发射火球或冰球
function handlePlayerShooting(playerState, playerElement, playerId) {
    // 如果按下发射键且可以发射
    if (playerState.keys.shoot && playerState.canShoot) {
        // 创建火球或冰球
        let projectile;
        if (playerState.hasIceBall) {
            projectile = createIceBall(playerState, playerId);
        } else {
            projectile = createFireball(playerState, playerId);
        }
        
        // 播放射击音效
        if (typeof soundGenerator !== 'undefined') {
            soundGenerator.generateShootSound();
        }
        
        fireballs.push(projectile);
        
        // 设置发射冷却
        playerState.canShoot = false;
        setTimeout(() => {
            playerState.canShoot = true;
        }, 500); // 0.5秒冷却时间
    }
}

// 更新火球位置并检测碰撞
function updateFireballs() {
    for (let i = fireballs.length - 1; i >= 0; i--) {
        const fireball = fireballs[i];
        
        // 如果火球不再活跃，则移除
        if (!fireball.active) {
            fireball.element.remove();
            fireballs.splice(i, 1);
            continue;
        }
        
        // 根据方向移动火球或冰球
        if (fireball.direction === 'right') {
            fireball.x += FIREBALL_SPEED;
        } else {
            fireball.x -= FIREBALL_SPEED;
        }
        
        // 更新火球位置
        fireball.element.style.left = fireball.x + 'px';
        
        // 检查火球是否超出边界
        if (fireball.x < 0 || fireball.x > GAME_WIDTH) {
            fireball.active = false;
            continue;
        }
        
        // 获取火球的边界框
        const fireballRect = fireball.element.getBoundingClientRect();
        
        // 检测火球与玩家的碰撞
        const player1Rect = player1.getBoundingClientRect();
        const player2Rect = player2.getBoundingClientRect();
        
        // 检查火球是否击中玩家（不能击中发射者自己）
        if (fireball.playerId === 2 && isColliding(fireballRect, player1Rect)) {
            // 玩家2的火球/冰球击中玩家1
            if (!player1State.isInvincible && !player1State.isDead) { // 检查玩家1是否无敌或死亡
                if (fireball.isIceBall) {
                    // 冰球效果 - 冰冻玩家
                    player1State.health = Math.max(0, player1State.health - 4); // 造成4点伤害
                    showFloatingText('-4 伤害 冰冻!', fireball.x, fireball.y, '#00BFFF');

                    // 冰冻效果 - 使玩家无法移动并变白
                    player1.style.backgroundColor = '#FFFFFF'; // 全身变白
                    player1.style.border = '2px solid #00BFFF';
                    player1.style.boxShadow = '0 0 10px #00BFFF';

                    // 设置冰冻状态，使玩家无法移动
                    player1State.isFrozen = true;

                    // 5秒后解除冰冻效果
                    setTimeout(() => {
                        // 检查玩家是否还存在且未死亡
                        if (player1 && !player1State.isDead) {
                            player1.style.backgroundColor = '#FF5555'; // 恢复原来的颜色
                            player1.style.border = '';
                            player1.style.boxShadow = '';
                            player1State.isFrozen = false; // 恢复移动能力
                            showFloatingText('解除冰冻!', player1State.x, player1State.y, '#00BFFF');
                        }
                    }, 5000);
                } else {
                    // 根据火球大小决定伤害
                    const damage = fireball.isLarge ? 10 : 1;
                    player1State.health = Math.max(0, player1State.health - damage);
                    showFloatingText(`-${damage} 伤害`, fireball.x, fireball.y, '#F44336');
                }

                // 震动效果
                player1.style.animation = 'shake 0.3s';
                setTimeout(() => {
                    if (player1) player1.style.animation = '';
                }, 300);

            } else if (player1State.isInvincible && !player1State.isDead) {
                 // 如果玩家无敌，显示一个不同的提示或不显示
                 showFloatingText('免疫!', fireball.x, fireball.y, '#FFFF00');
            }

            // 播放击中音效
            if (typeof soundGenerator !== 'undefined') {
                console.log('Playing hit sound - fireball hit player');
                soundGenerator.generateHitSound();
            } else {
                console.log('soundGenerator is undefined');
            }

            // 移除火球/冰球
            fireball.active = false;
        } else if (fireball.playerId === 1 && isColliding(fireballRect, player2Rect)) {
            // 玩家1的火球/冰球击中玩家2
            if (!player2State.isInvincible && !player2State.isDead) { // 检查玩家2是否无敌或死亡
                if (fireball.isIceBall) {
                    // 冰球效果 - 冰冻玩家
                    player2State.health = Math.max(0, player2State.health - 4); // 造成4点伤害
                    showFloatingText('-4 伤害 冰冻!', fireball.x, fireball.y, '#00BFFF');

                    // 冰冻效果 - 使玩家无法移动并变白
                    player2.style.backgroundColor = '#FFFFFF'; // 全身变白
                    player2.style.border = '2px solid #00BFFF';
                    player2.style.boxShadow = '0 0 10px #00BFFF';

                    // 设置冰冻状态，使玩家无法移动
                    player2State.isFrozen = true;

                    // 5秒后解除冰冻效果
                    setTimeout(() => {
                         // 检查玩家是否还存在且未死亡
                        if (player2 && !player2State.isDead) {
                            player2.style.backgroundColor = '#5555FF'; // 恢复原来的颜色
                            player2.style.border = '';
                            player2.style.boxShadow = '';
                            player2State.isFrozen = false; // 恢复移动能力
                            showFloatingText('解除冰冻!', player2State.x, player2State.y, '#00BFFF');
                        }
                    }, 5000);
                } else {
                    // 根据火球大小决定伤害
                    const damage = fireball.isLarge ? 10 : 1;
                    player2State.health = Math.max(0, player2State.health - damage);
                    showFloatingText(`-${damage} 伤害`, fireball.x, fireball.y, '#F44336');
                }

                // 震动效果
                player2.style.animation = 'shake 0.3s';
                 setTimeout(() => {
                    if (player2) player2.style.animation = '';
                }, 300);

            } else if (player2State.isInvincible && !player2State.isDead) {
                 // 如果玩家无敌，显示一个不同的提示或不显示
                 showFloatingText('免疫!', fireball.x, fireball.y, '#FFFF00');
            }

            // 播放击中音效
            if (typeof soundGenerator !== 'undefined') {
                console.log('Playing hit sound - fireball hit player 2');
                soundGenerator.generateHitSound();
            } else {
                console.log('soundGenerator is undefined');
            }

            // 移除火球/冰球
            fireball.active = false;
        }
    }
}

// 当页面加载完成后初始化游戏
window.addEventListener('load', initGame);

// 新增：检查玩家死亡状态
function checkPlayerDeath(playerState, playerElement) {
    if (playerState.health <= 0 && !playerState.isDead) {
        handlePlayerDeath(playerState, playerElement);
    }
}

// 新增：处理玩家死亡
function handlePlayerDeath(playerState, playerElement) {
    playerState.isDead = true;
    playerState.isMoving = false; // 确保停止移动
    playerElement.classList.add('dead'); // 应用死亡样式
    playerElement.style.zIndex = '1'; // 确保尸体在下层

    // 移除可能存在的冰冻效果
    playerState.isFrozen = false;
    playerElement.style.backgroundColor = ''; // 恢复默认背景色
    playerElement.style.border = '';
    playerElement.style.boxShadow = '';

    // 创建灵魂元素
    const soul = document.createElement('div');
    soul.className = 'soul';
    soul.style.left = playerState.x + (PLAYER_WIDTH / 2) - 10 + 'px'; // 灵魂起始位置在玩家中心
    soul.style.top = playerState.y + (PLAYER_HEIGHT / 2) - 10 + 'px';
    gameWorld.appendChild(soul);
    playerState.soulElement = soul; // 保存灵魂元素的引用

    // 播放死亡音效 (如果需要)
    // if (window.AudioManager) { AudioManager.playSound('death'); }

    // 5秒后复活
    setTimeout(() => {
        respawnPlayer(playerState, playerElement);
    }, 5000);
}

// 新增：复活玩家
function respawnPlayer(playerState, playerElement) {
    // 移除灵魂
    if (playerState.soulElement) {
        playerState.soulElement.remove();
        playerState.soulElement = null;
    }

    // 重置状态
    playerState.isDead = false;
    playerState.health = 100;
    playerElement.classList.remove('dead'); // 移除死亡样式
    playerElement.style.zIndex = '10'; // 恢复层级

    // 设置复活位置
    if (playerElement.id === 'player1') {
        playerState.x = 50; // 左侧中央
        playerState.y = GAME_HEIGHT / 2 - PLAYER_HEIGHT / 2;
    } else {
        playerState.x = GAME_WIDTH - PLAYER_WIDTH - 50; // 右侧中央
        playerState.y = GAME_HEIGHT / 2 - PLAYER_HEIGHT / 2;
    }

    // 更新玩家位置和状态显示
    updatePlayerPosition(playerElement, playerState);
    updatePlayerStatusDisplay();

    // 显示复活提示
    showFloatingText('复活!', playerState.x, playerState.y, '#4CAF50');
}

// 发射火球
function shootFireball(playerState, playerElement) {
    // 如果玩家死亡或无法射击，则不执行
    if (playerState.isDead || !playerState.canShoot) {
        return;
    }

    // 播放发射音效
    if (window.AudioManager) {
        AudioManager.playSound('movement');
    }
}

// 更新玩家状态显示
function updatePlayerStatusDisplay() {
    // 更新玩家1状态
    const player1Health = document.getElementById('player1-health');
    const player1HealthText = document.getElementById('player1-health-text');
    const player1Wealth = document.getElementById('player1-wealth');

    const p1CurrentHealth = player1State.isDead ? 0 : player1State.health; // 死亡时显示0
    player1Health.style.width = p1CurrentHealth + '%';
    player1HealthText.textContent = Math.round(p1CurrentHealth);
    player1Wealth.textContent = player1State.wealth;

    // 根据血量改变颜色
    if (p1CurrentHealth > 60) {
        player1Health.style.backgroundColor = '#4CAF50'; // 绿色
    } else if (p1CurrentHealth > 30) {
        player1Health.style.backgroundColor = '#FFC107'; // 黄色
    } else {
        player1Health.style.backgroundColor = '#F44336'; // 红色
    }

    // 更新玩家2状态
    const player2Health = document.getElementById('player2-health');
    const player2HealthText = document.getElementById('player2-health-text');
    const player2Wealth = document.getElementById('player2-wealth');

    const p2CurrentHealth = player2State.isDead ? 0 : player2State.health; // 死亡时显示0
    player2Health.style.width = p2CurrentHealth + '%';
    player2HealthText.textContent = Math.round(p2CurrentHealth);
    player2Wealth.textContent = player2State.wealth;

    // 根据血量改变颜色
    if (p2CurrentHealth > 60) {
        player2Health.style.backgroundColor = '#4CAF50'; // 绿色
    } else if (p2CurrentHealth > 30) {
        player2Health.style.backgroundColor = '#FFC107'; // 黄色
    } else {
        player2Health.style.backgroundColor = '#F44336'; // 红色
    }

    // 检查玩家1是否死亡
    if (player1State.health <= 0 && !player1State.isDead) {
        handlePlayerDeath(player1State, player1);
    }

    // 检查玩家2是否死亡
    if (player2State.health <= 0 && !player2State.isDead) {
        handlePlayerDeath(player2State, player2);
    }
}

// 处理玩家死亡
function handlePlayerDeath(playerState, playerElement) {
    playerState.isDead = true;
    playerElement.style.display = 'none'; // 隐藏玩家元素

    // 创建灵魂元素
    const soul = document.createElement('div');
    soul.className = 'soul';
    soul.style.position = 'absolute';
    soul.style.width = PLAYER_WIDTH + 'px';
    soul.style.height = PLAYER_HEIGHT + 'px';
    soul.style.left = playerState.x + 'px';
    soul.style.top = playerState.y + 'px';
    soul.style.backgroundImage = 'url(images/soul.svg)'; // 假设有一个灵魂的SVG图片
    soul.style.backgroundSize = 'cover';
    soul.style.zIndex = '10';
    soul.style.transition = 'transform 5s ease-out, opacity 5s ease-out';
    gameWorld.appendChild(soul);
    playerState.soulElement = soul; // 保存灵魂元素的引用

    // 灵魂飘走动画
    setTimeout(() => {
        soul.style.transform = 'translateY(-100px) scale(0.5)';
        soul.style.opacity = '0';
    }, 10);

    // 5秒后复活
    setTimeout(() => {
        // 移除灵魂元素
        if (playerState.soulElement) {
            playerState.soulElement.remove();
            playerState.soulElement = null;
        }

        // 重置玩家状态并复活
        playerState.health = 100; // 恢复血量
        playerState.isDead = false;
        playerState.isFrozen = false; // 确保不是冰冻状态
        playerState.isInvincible = false; // 确保不是无敌状态
        playerElement.style.display = 'block'; // 显示玩家元素
        // 可以选择将玩家移动到起始位置或随机位置
        // playerState.x = playerElement.id === 'player1' ? 100 : 200;
        // playerState.y = 100;
        // updatePlayerPosition(playerElement, playerState);
        showFloatingText('复活!', playerState.x + playerElement.offsetWidth / 2, playerState.y, '#00FF00');
    }, 5000); // 5000毫秒 = 5秒
}

// 碰撞检测辅助函数
function isColliding(rect1, rect2) {
    return (
        rect1.left < rect2.right &&
        rect1.right > rect2.left &&
        rect1.top < rect2.bottom &&
        rect1.bottom > rect2.top
    );
}

// 键盘事件监听
document.addEventListener('keydown', (e) => {
    handleKeyEvent(e, true);
});

document.addEventListener('keyup', (e) => {
    handleKeyEvent(e, false);
});

// 处理键盘事件
function handleKeyEvent(e, isKeyDown) {
    // 防止按键导致页面滚动
    if (['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight', 'w', 'a', 's', 'd', 'W', 'A', 'S', 'D', ' ', '0'].includes(e.key)) {
        e.preventDefault();
    }
    
    // 玩家1 - 方向键和数字0键发射火球
    switch (e.key) {
        case 'ArrowUp':
            player1State.keys.up = isKeyDown;
            break;
        case 'ArrowDown':
            player1State.keys.down = isKeyDown;
            break;
        case 'ArrowLeft':
            player1State.keys.left = isKeyDown;
            break;
        case 'ArrowRight':
            player1State.keys.right = isKeyDown;
            break;
        case '0': // 数字0键发射火球
            player1State.keys.shoot = isKeyDown;
            break;
    }
    
    // 玩家2 - WASD和空格键发射火球
    switch (e.key.toLowerCase()) {
        case 'w':
            player2State.keys.up = isKeyDown;
            break;
        case 's':
            player2State.keys.down = isKeyDown;
            break;
        case 'a':
            player2State.keys.left = isKeyDown;
            break;
        case 'd':
            player2State.keys.right = isKeyDown;
            break;
        case ' ': // 空格键发射火球
            player2State.keys.shoot = isKeyDown;
            break;
    }
}

// 更新玩家动画状态
function updatePlayerAnimation(playerElement, playerState) {
    // 如果玩家死亡，应用死亡样式
    if (playerState.isDead) {
        playerElement.classList.add('dead'); // 添加死亡状态类
        playerElement.classList.remove('moving');
        return; // 死亡状态下不执行其他动画逻辑
    }

    // 移除死亡状态类（如果存在）
    playerElement.classList.remove('dead');

    // 根据移动状态添加或移除 'moving' 类
    if (playerState.isMoving) {
        playerElement.classList.add('moving');
    } else {
        playerElement.classList.remove('moving');
    }
}

// 创建火球元素
function createFireball(playerState, playerId) {
    // 创建火球元素
    const fireball = document.createElement('div');
    fireball.className = 'fireball';
    
    // 设置火球样式
    let currentFireballWidth = FIREBALL_WIDTH;
    let currentFireballHeight = FIREBALL_HEIGHT;

    // 如果玩家处于变大状态，火球也变大
    if (playerState.isEnlarged) {
        currentFireballWidth *= 4;
        currentFireballHeight *= 4;
    }

    fireball.style.width = currentFireballWidth + 'px';
    fireball.style.height = currentFireballHeight + 'px';
    fireball.style.position = 'absolute';
    fireball.style.borderRadius = '50%';
    fireball.style.zIndex = '8';
    
    // 根据玩家设置火球颜色
    if (playerId === 1) {
        fireball.style.backgroundColor = '#FF7F50'; // 红色玩家的火球颜色
        fireball.style.boxShadow = '0 0 10px #FF4500';
    } else {
        fireball.style.backgroundColor = '#4F94CD'; // 蓝色玩家的火球颜色
        fireball.style.boxShadow = '0 0 10px #1E90FF';
    }
    
    // 获取发射器元素
    const launcherId = playerId === 1 ? 'launcher1' : 'launcher2';
    const launcher = document.getElementById(launcherId);
    const playerElement = playerId === 1 ? player1 : player2;
    
    // 计算火球初始位置（从发射器位置发射）
    let fireballX, fireballY;
    
    if (launcher) {
        // 获取发射器的位置信息
        const playerRect = playerElement.getBoundingClientRect();
        const launcherRect = launcher.getBoundingClientRect();
        
        // 计算发射器相对于游戏世界的位置
        const gameWorldRect = gameWorld.getBoundingClientRect();
        const launcherAbsX = launcherRect.left - gameWorldRect.left;
        const launcherAbsY = launcherRect.top - gameWorldRect.top;
        
        // 根据玩家方向设置火球位置
        if (playerState.direction === 'right') {
            fireballX = launcherAbsX + launcher.offsetWidth;
            fireballY = launcherAbsY + launcher.offsetHeight / 2 - FIREBALL_HEIGHT / 2;
        } else {
            fireballX = launcherAbsX - FIREBALL_WIDTH;
            fireballY = launcherAbsY + launcher.offsetHeight / 2 - FIREBALL_HEIGHT / 2;
        }
    } else {
        // 如果找不到发射器，则使用玩家位置作为备用
        if (playerState.direction === 'right') {
            fireballX = playerState.x + PLAYER_WIDTH;
            fireballY = playerState.y + PLAYER_HEIGHT / 2 - FIREBALL_HEIGHT / 2;
        } else {
            fireballX = playerState.x - FIREBALL_WIDTH;
            fireballY = playerState.y + PLAYER_HEIGHT / 2 - FIREBALL_HEIGHT / 2;
        }
    }
    
    // 设置火球位置
    fireball.style.left = fireballX + 'px';
    fireball.style.top = fireballY + 'px';
    
    // 添加到游戏世界
    gameWorld.appendChild(fireball);
    
    // 播放发射音效
    if (window.AudioManager) {
        AudioManager.playSound('movement'); // 使用现有的音效
    }
    
    // 返回火球数据
    return {
        element: fireball,
        x: fireballX,
        y: fireballY,
        direction: playerState.direction,
        playerId: playerId, // 记录是哪个玩家发射的
        active: true,
        isLarge: playerState.isEnlarged // 标记是否为大火球
    };
}

// 创建冰球元素
function createIceBall(playerState, playerId) {
    // 创建冰球元素
    const iceball = document.createElement('div');
    iceball.className = 'iceball';
    
    // 设置冰球样式
    iceball.style.width = FIREBALL_WIDTH + 'px';
    iceball.style.height = FIREBALL_HEIGHT + 'px';
    iceball.style.position = 'absolute';
    iceball.style.borderRadius = '50%';
    iceball.style.zIndex = '8';
    
    // 设置冰球颜色和效果
    iceball.style.backgroundColor = '#00BFFF'; // 冰蓝色
    iceball.style.boxShadow = '0 0 10px #87CEFA';
    
    // 获取发射器元素
    const launcherId = playerId === 1 ? 'launcher1' : 'launcher2';
    const launcher = document.getElementById(launcherId);
    const playerElement = playerId === 1 ? player1 : player2;
    
    // 计算冰球初始位置（从发射器位置发射）
    let iceballX, iceballY;
    
    if (launcher) {
        // 获取发射器的位置信息
        const playerRect = playerElement.getBoundingClientRect();
        const launcherRect = launcher.getBoundingClientRect();
        
        // 计算发射器相对于游戏世界的位置
        const gameWorldRect = gameWorld.getBoundingClientRect();
        const launcherAbsX = launcherRect.left - gameWorldRect.left;
        const launcherAbsY = launcherRect.top - gameWorldRect.top;
        
        // 根据玩家方向设置冰球位置
        if (playerState.direction === 'right') {
            iceballX = launcherAbsX + launcher.offsetWidth;
            iceballY = launcherAbsY + launcher.offsetHeight / 2 - FIREBALL_HEIGHT / 2;
        } else {
            iceballX = launcherAbsX - FIREBALL_WIDTH;
            iceballY = launcherAbsY + launcher.offsetHeight / 2 - FIREBALL_HEIGHT / 2;
        }
    } else {
        // 如果找不到发射器，则使用玩家位置作为备用
        if (playerState.direction === 'right') {
            iceballX = playerState.x + PLAYER_WIDTH;
            iceballY = playerState.y + PLAYER_HEIGHT / 2 - FIREBALL_HEIGHT / 2;
        } else {
            iceballX = playerState.x - FIREBALL_WIDTH;
            iceballY = playerState.y + PLAYER_HEIGHT / 2 - FIREBALL_HEIGHT / 2;
        }
    }
    
    // 设置冰球位置
    iceball.style.left = iceballX + 'px';
    iceball.style.top = iceballY + 'px';
    
    // 添加到游戏世界
    gameWorld.appendChild(iceball);
    
    // 播放发射音效
    if (window.AudioManager) {
        AudioManager.playSound('movement'); // 使用现有的音效
    }
    
    // 返回冰球数据
    return {
        element: iceball,
        x: iceballX,
        y: iceballY,
        direction: playerState.direction,
        playerId: playerId, // 记录是哪个玩家发射的
        active: true,
        isIceBall: true // 标记为冰球
    };
}

// 处理玩家发射火球或冰球
function handlePlayerShooting(playerState, playerElement, playerId) {
    // 如果按下发射键且可以发射
    if (playerState.keys.shoot && playerState.canShoot) {
        // 根据玩家状态决定发射火球还是冰球
        let projectile;
        if (playerState.hasIceBall) {
            projectile = createIceBall(playerState, playerId);
        } else {
            projectile = createFireball(playerState, playerId);
        }
        
        // 播放射击音效
        if (typeof soundGenerator !== 'undefined') {
            soundGenerator.generateShootSound();
        }
        
        fireballs.push(projectile);
        
        // 设置发射冷却
        playerState.canShoot = false;
        setTimeout(() => {
            playerState.canShoot = true;
        }, 500); // 0.5秒冷却时间
    }
}

// 更新火球位置并检测碰撞
function updateFireballs() {
    for (let i = fireballs.length - 1; i >= 0; i--) {
        const fireball = fireballs[i];
        
        // 如果火球不再活跃，则移除
        if (!fireball.active) {
            fireball.element.remove();
            fireballs.splice(i, 1);
            continue;
        }
        
        // 根据方向移动火球或冰球
        if (fireball.direction === 'right') {
            fireball.x += FIREBALL_SPEED;
        } else {
            fireball.x -= FIREBALL_SPEED;
        }
        
        // 更新火球位置
        fireball.element.style.left = fireball.x + 'px';
        
        // 检查火球是否超出边界
        if (fireball.x < 0 || fireball.x > GAME_WIDTH) {
            fireball.active = false;
            continue;
        }
        
        // 获取火球的边界框
        const fireballRect = fireball.element.getBoundingClientRect();
        
        // 检测火球与玩家的碰撞
        const player1Rect = player1.getBoundingClientRect();
        const player2Rect = player2.getBoundingClientRect();
        
        // 检查火球是否击中玩家（不能击中发射者自己）
        if (fireball.playerId === 2 && isColliding(fireballRect, player1Rect)) {
            // 玩家2的火球/冰球击中玩家1
            if (!player1State.isInvincible && !player1State.isDead) { // 检查玩家1是否无敌或死亡
                if (fireball.isIceBall) {
                    // 冰球效果 - 冰冻玩家
                    player1State.health = Math.max(0, player1State.health - 4); // 造成4点伤害
                    showFloatingText('-4 伤害 冰冻!', fireball.x, fireball.y, '#00BFFF');

                    // 冰冻效果 - 使玩家无法移动并变白
                    player1.style.backgroundColor = '#FFFFFF'; // 全身变白
                    player1.style.border = '2px solid #00BFFF';
                    player1.style.boxShadow = '0 0 10px #00BFFF';

                    // 设置冰冻状态，使玩家无法移动
                    player1State.isFrozen = true;

                    // 5秒后解除冰冻效果
                    setTimeout(() => {
                        // 检查玩家是否还存在且未死亡
                        if (player1 && !player1State.isDead) {
                            player1.style.backgroundColor = '#FF5555'; // 恢复原来的颜色
                            player1.style.border = '';
                            player1.style.boxShadow = '';
                            player1State.isFrozen = false; // 恢复移动能力
                            showFloatingText('解除冰冻!', player1State.x, player1State.y, '#00BFFF');
                        }
                    }, 5000);
                } else {
                    // 根据火球大小决定伤害
                    const damage = fireball.isLarge ? 10 : 1;
                    player1State.health = Math.max(0, player1State.health - damage);
                    showFloatingText(`-${damage} 伤害`, fireball.x, fireball.y, '#F44336');
                }

                // 震动效果
                player1.style.animation = 'shake 0.3s';
                setTimeout(() => {
                    if (player1) player1.style.animation = '';
                }, 300);

            } else if (player1State.isInvincible && !player1State.isDead) {
                 // 如果玩家无敌，显示一个不同的提示或不显示
                 showFloatingText('免疫!', fireball.x, fireball.y, '#FFFF00');
            }

            // 播放碰撞音效 (即使无敌或死亡也播放)
            if (window.AudioManager) {
                AudioManager.playSound('collision');
            }

            // 移除火球/冰球
            fireball.active = false;
        } else if (fireball.playerId === 1 && isColliding(fireballRect, player2Rect)) {
            // 玩家1的火球/冰球击中玩家2
            if (!player2State.isInvincible && !player2State.isDead) { // 检查玩家2是否无敌或死亡
                if (fireball.isIceBall) {
                    // 冰球效果 - 冰冻玩家
                    player2State.health = Math.max(0, player2State.health - 4); // 造成4点伤害
                    showFloatingText('-4 伤害 冰冻!', fireball.x, fireball.y, '#00BFFF');

                    // 冰冻效果 - 使玩家无法移动并变白
                    player2.style.backgroundColor = '#FFFFFF'; // 全身变白
                    player2.style.border = '2px solid #00BFFF';
                    player2.style.boxShadow = '0 0 10px #00BFFF';

                    // 设置冰冻状态，使玩家无法移动
                    player2State.isFrozen = true;

                    // 5秒后解除冰冻效果
                    setTimeout(() => {
                         // 检查玩家是否还存在且未死亡
                        if (player2 && !player2State.isDead) {
                            player2.style.backgroundColor = '#5555FF'; // 恢复原来的颜色
                            player2.style.border = '';
                            player2.style.boxShadow = '';
                            player2State.isFrozen = false; // 恢复移动能力
                            showFloatingText('解除冰冻!', player2State.x, player2State.y, '#00BFFF');
                        }
                    }, 5000);
                } else {
                    // 根据火球大小决定伤害
                    const damage = fireball.isLarge ? 10 : 1;
                    player2State.health = Math.max(0, player2State.health - damage);
                    showFloatingText(`-${damage} 伤害`, fireball.x, fireball.y, '#F44336');
                }

                // 震动效果
                player2.style.animation = 'shake 0.3s';
                 setTimeout(() => {
                    if (player2) player2.style.animation = '';
                }, 300);

            } else if (player2State.isInvincible && !player2State.isDead) {
                 // 如果玩家无敌，显示一个不同的提示或不显示
                 showFloatingText('免疫!', fireball.x, fireball.y, '#FFFF00');
            }

            // 播放碰撞音效 (即使无敌或死亡也播放)
            if (window.AudioManager) {
                AudioManager.playSound('collision');
            }

            // 移除火球/冰球
            fireball.active = false;
        }
    }
}

// 当页面加载完成后初始化游戏
window.addEventListener('load', initGame);