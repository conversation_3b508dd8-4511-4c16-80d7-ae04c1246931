{"name": "midi-writer-js", "version": "3.1.1", "description": "A library providing an API for generating MIDI files.", "main": "build/index.js", "types": "build/types/main.d.ts", "dependencies": {"@tonaljs/midi": "^4.9.0"}, "devDependencies": {"@babel/core": "^7.9.6", "@babel/plugin-transform-destructuring": "^7.9.5", "@babel/preset-env": "^7.9.6", "@rollup/plugin-babel": "^5.0.0", "@rollup/plugin-node-resolve": "^15.0.1", "@rollup/plugin-replace": "^2.4.2", "@rollup/plugin-typescript": "^11.0.0", "@types/node": "^18.15.3", "@typescript-eslint/eslint-plugin": "^5.55.0", "@typescript-eslint/parser": "^5.55.0", "eslint": "^7.32.0", "eslint-config-standard": "^12.0.0", "eslint-plugin-import": "^2.20.2", "eslint-plugin-node": "^9.2.0", "eslint-plugin-promise": "^4.1.1", "eslint-plugin-standard": "^4.0.1", "jsdoc": "^3.6.4", "minami": "^1.2.3", "mocha": "^9.0.1", "nyc": "^15.0.1", "rollup": "^2.9.0", "tslib": "^2.5.0", "typedoc": "^0.25.1", "typescript": "^5.0.2", "watch": "^1.0.2"}, "directories": {"lib": "src", "example": "examples", "test": "test"}, "scripts": {"build": "mkdir -p build && rollup -c", "docs": "npx typedoc --options typedoc.json", "lint:js": "eslint 'src/**/**.ts'", "prepublishOnly": "npm test", "pretest": "npm run build", "test": "nyc --reporter=text mocha --no-config --no-package", "watch": "watch 'npm run build' src", "postinstall": "node postinstall.js"}, "runkitExampleFilename": "runkit.js", "repository": {"type": "git", "url": "git+https://github.com/grimmdude/MidiWriterJS.git"}, "keywords": ["midi", "generator", "music"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/grimmdude/MidiWriterJS/issues"}, "homepage": "https://github.com/grimmdude/MidiWriterJS#readme", "publishConfig": {"@grimmdude:registry": "https://npm.pkg.github.com"}}