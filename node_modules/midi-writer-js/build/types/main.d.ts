import { ControllerChangeEvent } from './midi-events/controller-change-event';
import { CopyrightEvent } from './meta-events/copyright-event';
import { CuePointEvent } from './meta-events/cue-point-event';
import { EndTrackEvent } from './meta-events/end-track-event';
import { InstrumentNameEvent } from './meta-events/instrument-name-event';
import { KeySignatureEvent } from './meta-events/key-signature-event';
import { LyricEvent } from './meta-events/lyric-event';
import { MarkerEvent } from './meta-events/marker-event';
import { NoteOnEvent } from './midi-events/note-on-event';
import { NoteOffEvent } from './midi-events/note-off-event';
import { NoteEvent } from './midi-events/note-event';
import { PitchBendEvent } from './midi-events/pitch-bend-event';
import { ProgramChangeEvent } from './midi-events/program-change-event';
import { TempoEvent } from './meta-events/tempo-event';
import { TextEvent } from './meta-events/text-event';
import { TimeSignatureEvent } from './meta-events/time-signature-event';
import { Track } from './chunks/track';
import { TrackNameEvent } from './meta-events/track-name-event';
import { Utils } from './utils';
import { VexFlow } from './vexflow';
import { Writer } from './writer';
declare const _default: {
    Constants: {
        VERSION: string;
        HEADER_CHUNK_TYPE: number[];
        HEADER_CHUNK_LENGTH: number[];
        HEADER_CHUNK_FORMAT0: number[];
        HEADER_CHUNK_FORMAT1: number[];
        HEADER_CHUNK_DIVISION: number[];
        TRACK_CHUNK_TYPE: number[];
        META_EVENT_ID: number;
        META_SMTPE_OFFSET: number;
    };
    ControllerChangeEvent: typeof ControllerChangeEvent;
    CopyrightEvent: typeof CopyrightEvent;
    CuePointEvent: typeof CuePointEvent;
    EndTrackEvent: typeof EndTrackEvent;
    InstrumentNameEvent: typeof InstrumentNameEvent;
    KeySignatureEvent: typeof KeySignatureEvent;
    LyricEvent: typeof LyricEvent;
    MarkerEvent: typeof MarkerEvent;
    NoteOnEvent: typeof NoteOnEvent;
    NoteOffEvent: typeof NoteOffEvent;
    NoteEvent: typeof NoteEvent;
    PitchBendEvent: typeof PitchBendEvent;
    ProgramChangeEvent: typeof ProgramChangeEvent;
    TempoEvent: typeof TempoEvent;
    TextEvent: typeof TextEvent;
    TimeSignatureEvent: typeof TimeSignatureEvent;
    Track: typeof Track;
    TrackNameEvent: typeof TrackNameEvent;
    Utils: typeof Utils;
    VexFlow: typeof VexFlow;
    Writer: typeof Writer;
};
export default _default;
