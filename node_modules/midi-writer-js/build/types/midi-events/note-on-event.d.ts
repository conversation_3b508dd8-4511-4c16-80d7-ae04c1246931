import { MidiEvent } from './midi-event';
/**
 * Holds all data for a "note on" MIDI event
 * @param {object} fields {data: []}
 * @return {NoteOnEvent}
 */
declare class NoteOnEvent implements MidiEvent {
    channel: number;
    data: number[];
    delta: number;
    status: 0x90;
    name: string;
    pitch: string | string[] | number | number[];
    velocity: number;
    wait: string | number;
    tick: number;
    deltaWithPrecisionCorrection: number;
    constructor(fields: {
        channel?: number;
        wait?: string | number;
        velocity?: number;
        pitch?: string | string[] | number | number[];
        tick?: number;
        data?: number[];
        delta?: number;
    });
    /**
     * Builds int array for this event.
     * @param {Track} track - parent track
     * @return {NoteOnEvent}
     */
    buildData(track: any, precisionDelta: any, options?: {
        middleC?: string;
    }): this;
}
export { NoteOnEvent };
