import { MidiEvent } from './midi-event';
/**
 * Holds all data for a "Pitch Bend" MIDI event
 * [ -1.0, 0, 1.0 ] ->  [ 0, 8192, 16383]
 * @param {object} fields { bend : float, channel : int, delta: int }
 * @return {PitchBendEvent}
 */
declare class PitchBendEvent implements MidiEvent {
    channel: number;
    data: number[];
    delta: number;
    name: string;
    status: 0xE0;
    constructor(fields: any);
    scale14bits(zeroOne: any): number;
}
export { PitchBendEvent };
