import { MidiEvent } from './midi-event';
/**
 * Holds all data for a "program change" MIDI event
 * @param {object} fields {instrument: integer, delta: integer}
 * @return {ProgramChangeEvent}
 */
declare class ProgramChangeEvent implements MidiEvent {
    channel: number;
    data: number[];
    delta: number;
    status: 0xC0;
    name: string;
    instrument: number;
    constructor(fields: {
        channel?: number;
        delta?: number;
        instrument: number;
    });
}
export { ProgramChangeEvent };
