import { MidiEvent } from './midi-event';
/**
 * Holds all data for a "controller change" MIDI event
 * @param {object} fields {controllerNumber: integer, controllerValue: integer, delta: integer}
 * @return {ControllerChangeEvent}
 */
declare class ControllerChangeEvent implements MidiEvent {
    channel: number;
    controllerNumber: number;
    controllerValue: number;
    data: number[];
    delta: number;
    name: string;
    status: 0xB0;
    constructor(fields: {
        controllerNumber: number;
        controllerValue: number;
        channel?: number;
        delta?: number;
    });
}
export { ControllerChangeEvent };
