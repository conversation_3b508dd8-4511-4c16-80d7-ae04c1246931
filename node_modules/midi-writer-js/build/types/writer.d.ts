import { Track } from './chunks/track';
/**
 * Object that puts together tracks and provides methods for file output.
 * @param {array|Track} tracks - A single {Track} object or an array of {Track} objects.
 * @param {object} options - {middleC: 'C4'}
 * @return {Writer}
 */
declare class Writer {
    tracks: Track[];
    options: object;
    constructor(tracks: any, options?: {});
    /**
     * Builds array of data from chunkschunks.
     * @return {array}
     */
    buildData(): any[];
    /**
     * Builds the file into a Uint8Array
     * @return {Uint8Array}
     */
    buildFile(): Uint8Array;
    /**
     * Convert file buffer to a base64 string.  Different methods depending on if browser or node.
     * @return {string}
     */
    base64(): string;
    /**
     * Get the data URI.
     * @return {string}
     */
    dataUri(): string;
    /**
     * Set option on instantiated Writer.
     * @param {string} key
     * @param {any} value
     * @return {Writer}
     */
    setOption(key: string, value: number | string): Writer;
    /**
     * Output to stdout
     * @return {string}
     */
    stdout(): boolean;
}
export { Writer };
