/**
 * Static utility functions used throughout the library.
 */
declare class Utils {
    /**
     * Gets MidiWriterJS version number.
     * @return {string}
     */
    static version(): string;
    /**
     * Convert a string to an array of bytes
     * @param {string} string
     * @return {array}
     */
    static stringToBytes(string: string): number[];
    /**
     * Checks if argument is a valid number.
     * @param {*} n - Value to check
     * @return {boolean}
     */
    static isNumeric(n: any): boolean;
    /**
     * Returns the correct MIDI number for the specified pitch.
     * Uses Tonal Midi - https://github.com/danigb/tonal/tree/master/packages/midi
     * @param {(string|number)} pitch - 'C#4' or midi note code
     * @param {string} middleC
     * @return {number}
     */
    static getPitch(pitch: any, middleC?: string): number;
    /**
     * Translates number of ticks to MIDI timestamp format, returning an array of
     * hex strings with the time values. Midi has a very particular time to express time,
     * take a good look at the spec before ever touching this function.
     * Thanks to https://github.com/sergi/jsmidi
     *
     * @param {number} ticks - Number of ticks to be translated
     * @return {array} - Bytes that form the MIDI time value
     */
    static numberToVariableLength(ticks: number): number[];
    /**
     * Counts number of bytes in string
     * @param {string} s
     * @return {number}
     */
    static stringByteCount(s: string): number;
    /**
     * Get an int from an array of bytes.
     * @param {array} bytes
     * @return {number}
     */
    static numberFromBytes(bytes: number[]): number;
    /**
     * Takes a number and splits it up into an array of bytes.  Can be padded by passing a number to bytesNeeded
     * @param {number} number
     * @param {number} bytesNeeded
     * @return {array} - Array of bytes
     */
    static numberToBytes(number: number, bytesNeeded: number): number[];
    /**
     * Converts value to array if needed.
     * @param {any} value
     * @return {array}
     */
    static toArray(value: any): any[];
    /**
     * Converts velocity to value 0-127
     * @param {number} velocity - Velocity value 1-100
     * @return {number}
     */
    static convertVelocity(velocity: number): number;
    /**
     * Gets the total number of ticks of a specified duration.
     * Note: type=='note' defaults to quarter note, type==='rest' defaults to 0
     * @param {(string|array)} duration
     * @return {number}
     */
    static getTickDuration(duration: (string | string[] | number)): number;
    /**
     * Due to rounding errors in JavaScript engines,
     * it's safe to round when we're very close to the actual tick number
     *
     * @static
     * @param {number} tick
     * @return {number}
     */
    static getRoundedIfClose(tick: number): number;
    /**
     * Due to low precision of MIDI,
     * we need to keep track of rounding errors in deltas.
     * This function will calculate the rounding error for a given duration.
     *
     * @static
     * @param {number} tick
     * @return {number}
     */
    static getPrecisionLoss(tick: number): number;
    /**
     * Gets what to multiple ticks/quarter note by to get the specified duration.
     * Note: type=='note' defaults to quarter note, type==='rest' defaults to 0
     * @param {string} duration
     * @return {number}
     */
    static getDurationMultiplier(duration: string): number;
}
export { Utils };
