import { MetaEvent } from './meta-event';
/**
 * Object representation of a lyric meta event.
 * @param {object} fields {text: string, delta: integer}
 * @return {LyricEvent}
 */
declare class LyricEvent implements MetaEvent {
    data: number[];
    delta: number;
    name: string;
    text: string;
    type: 0x05;
    constructor(fields: {
        text: string;
        delta?: number;
    });
}
export { LyricEvent };
