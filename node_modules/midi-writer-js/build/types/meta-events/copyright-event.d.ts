import { MetaEvent } from './meta-event';
/**
 * Object representation of a tempo meta event.
 * @param {object} fields {text: string, delta: integer}
 * @return {CopyrightEvent}
 */
declare class CopyrightEvent implements MetaEvent {
    data: number[];
    delta: number;
    name: string;
    text: string;
    type: 0x02;
    constructor(fields: {
        text: string;
        delta?: number;
    });
}
export { CopyrightEvent };
