import { MetaEvent } from './meta-event';
/**
 * Object representation of a tempo meta event.
 * @param {object} fields {text: string, delta: integer}
 * @return {TextEvent}
 */
declare class TextEvent implements MetaEvent {
    data: number[];
    delta: number;
    name: string;
    text: string;
    type: 0x01;
    constructor(fields: {
        text: string;
        delta?: number;
    });
}
export { TextEvent };
