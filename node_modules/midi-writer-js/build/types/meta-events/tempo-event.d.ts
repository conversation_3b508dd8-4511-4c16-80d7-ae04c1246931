import { MetaEvent } from './meta-event';
/**
 * Object representation of a tempo meta event.
 * @param {object} fields {bpm: integer, delta: integer}
 * @return {TempoEvent}
 */
declare class TempoEvent implements MetaEvent {
    bpm: number;
    data: number[];
    delta: number;
    name: string;
    tick: number;
    type: 0x51;
    constructor(fields: {
        bpm: number;
        tick?: number;
        delta?: number;
    });
}
export { TempoEvent };
