import { MetaEvent } from './meta-event';
/**
 * Object representation of a tempo meta event.
 * @param {object} fields {text: string, delta: integer}
 * @return {TrackNameEvent}
 */
declare class TrackNameEvent implements MetaEvent {
    data: number[];
    delta: number;
    name: string;
    text: string;
    type: 0x03;
    constructor(fields: {
        text: string;
        delta?: number;
    });
}
export { TrackNameEvent };
