import { AbstractEvent } from '../abstract-event';
import { Chunk } from './chunk';
import { NoteEvent } from '../midi-events/note-event';
/**
 * Holds all data for a track.
 * @param {object} fields {type: number, data: array, size: array, events: array}
 * @return {Track}
 */
declare class Track implements Chunk {
    data: number[];
    events: AbstractEvent[];
    explicitTickEvents: NoteEvent[];
    size: number[];
    type: number[];
    tickPointer: number;
    constructor();
    /**
     * Adds any event type to the track.
     * Events without a specific startTick property are assumed to be added in order of how they should output.
     * Events with a specific startTick property are set aside for now will be merged in during build process.
     *
     * TODO: Don't put startTick events in their own array.  Just lump everything together and sort it out during buildData();
     * @param {(NoteEvent|ProgramChangeEvent)} events - Event object or array of Event objects.
     * @param {Function} mapFunction - Callback which can be used to apply specific properties to all events.
     * @return {Track}
     */
    addEvent(events: (AbstractEvent | AbstractEvent[]), mapFunction?: (i: number, event: AbstractEvent) => object): Track;
    /**
     * Builds int array of all events.
     * @param {object} options
     * @return {Track}
     */
    buildData(options?: {}): this;
    mergeExplicitTickEvents(): void;
    /**
     * Merges another track's events with this track.
     * @param {Track} track
     * @return {Track}
     */
    mergeTrack(track: Track): Track;
    /**
     * Merges a single event into this track's list of events based on event.tick property.
     * @param {AbstractEvent} - event
     * @return {Track}
     */
    mergeSingleEvent(event: AbstractEvent): Track;
    /**
     * Removes all events matching specified type.
     * @param {string} eventName - Event type
     * @return {Track}
     */
    removeEventsByName(eventName: string): Track;
    /**
     * Sets tempo of the MIDI file.
     * @param {number} bpm - Tempo in beats per minute.
     * @param {number} tick - Start tick.
     * @return {Track}
     */
    setTempo(bpm: number, tick?: number): Track;
    /**
     * Sets time signature.
     * @param {number} numerator - Top number of the time signature.
     * @param {number} denominator - Bottom number of the time signature.
     * @param {number} midiclockspertick - Defaults to 24.
     * @param {number} notespermidiclock - Defaults to 8.
     * @return {Track}
     */
    setTimeSignature(numerator: number, denominator: number, midiclockspertick: number, notespermidiclock: number): Track;
    /**
     * Sets key signature.
     * @param {*} sf -
     * @param {*} mi -
     * @return {Track}
     */
    setKeySignature(sf: any, mi: any): Track;
    /**
     * Adds text to MIDI file.
     * @param {string} text - Text to add.
     * @return {Track}
     */
    addText(text: string): Track;
    /**
     * Adds copyright to MIDI file.
     * @param {string} text - Text of copyright line.
     * @return {Track}
     */
    addCopyright(text: string): Track;
    /**
     * Adds Sequence/Track Name.
     * @param {string} text - Text of track name.
     * @return {Track}
     */
    addTrackName(text: string): Track;
    /**
     * Sets instrument name of track.
     * @param {string} text - Name of instrument.
     * @return {Track}
     */
    addInstrumentName(text: string): Track;
    /**
     * Adds marker to MIDI file.
     * @param {string} text - Marker text.
     * @return {Track}
     */
    addMarker(text: string): Track;
    /**
     * Adds cue point to MIDI file.
     * @param {string} text - Text of cue point.
     * @return {Track}
     */
    addCuePoint(text: string): Track;
    /**
     * Adds lyric to MIDI file.
     * @param {string} text - Lyric text to add.
     * @return {Track}
     */
    addLyric(text: string): Track;
    /**
     * Channel mode messages
     * @return {Track}
     */
    polyModeOn(): Track;
    /**
     * Sets a pitch bend.
     * @param {float} bend - Bend value ranging [-1,1], zero meaning no bend.
     * @return {Track}
     */
    setPitchBend(bend: number): Track;
    /**
     * Adds a controller change event
     * @param {number} number - Control number.
     * @param {number} value - Control value.
     * @param {number} channel - Channel to send controller change event on (1-based).
     * @param {number} delta - Track tick offset for cc event.
     * @return {Track}
     */
    controllerChange(number: number, value: number, channel?: number, delta?: number): Track;
}
export { Track };
