import { MetaEvent } from './meta-event';
/**
 * Object representation of an instrument name meta event.
 * @param {object} fields {text: string, delta: integer}
 * @return {InstrumentNameEvent}
 */
declare class InstrumentNameEvent implements MetaEvent {
    data: number[];
    delta: number;
    name: string;
    text: string;
    type: 0x04;
    constructor(fields: {
        text: string;
        delta?: number;
    });
}
export { InstrumentNameEvent };
