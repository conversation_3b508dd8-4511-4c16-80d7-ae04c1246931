import { MetaEvent } from './meta-event';
/**
 * Object representation of a cue point meta event.
 * @param {object} fields {text: string, delta: integer}
 * @return {CuePointEvent}
 */
declare class CuePointEvent implements MetaEvent {
    data: number[];
    delta: number;
    name: string;
    text: string;
    type: 0x07;
    constructor(fields: {
        text: string;
        delta?: number;
    });
}
export { CuePointEvent };
