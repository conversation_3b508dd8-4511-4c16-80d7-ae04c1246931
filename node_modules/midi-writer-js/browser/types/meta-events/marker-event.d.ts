import { MetaEvent } from './meta-event';
/**
 * Object representation of a marker meta event.
 * @param {object} fields {text: string, delta: integer}
 * @return {MarkerEvent}
 */
declare class MarkerEvent implements MetaEvent {
    data: number[];
    delta: number;
    name: string;
    text: string;
    type: 0x06;
    constructor(fields: {
        text: string;
        delta?: number;
    });
}
export { MarkerEvent };
