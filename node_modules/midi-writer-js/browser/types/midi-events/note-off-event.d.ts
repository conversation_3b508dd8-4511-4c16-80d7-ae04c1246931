import { MidiEvent } from './midi-event';
/**
 * Holds all data for a "note off" MIDI event
 * @param {object} fields {data: []}
 * @return {NoteOffEvent}
 */
declare class NoteOffEvent implements MidiEvent {
    channel: number;
    data: number[];
    delta: number;
    deltaWithPrecisionCorrection: number;
    status: 0x80;
    name: string;
    velocity: number;
    pitch: string | number;
    duration: string | number;
    tick: number;
    constructor(fields: {
        channel: number;
        duration: string | number;
        velocity: number;
        pitch: string | number;
        tick?: number;
        data?: number[];
        delta?: number;
    });
    /**
     * Builds int array for this event.
     * @param {Track} track - parent track
     * @return {NoteOffEvent}
     */
    buildData(track: any, precisionDelta: number, options?: {
        middleC?: string;
    }): this;
}
export { NoteOffEvent };
