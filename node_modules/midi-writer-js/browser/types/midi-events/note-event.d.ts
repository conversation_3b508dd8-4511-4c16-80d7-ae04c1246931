import { AbstractEvent } from '../abstract-event';
import { MidiEvent } from './midi-event';
/**
 * Wrapper for noteOnEvent/noteOffEvent objects that builds both events.
 * @param {object} fields - {pitch: '[C4]', duration: '4', wait: '4', velocity: 1-100}
 * @return {NoteEvent}
 */
declare class NoteEvent implements AbstractEvent {
    data: number[];
    delta: number;
    events: MidiEvent[];
    name: string;
    pitch: string[];
    grace: string | string[];
    channel: number;
    repeat: number;
    tick: number;
    duration: string;
    sequential: boolean;
    wait: string;
    velocity: number;
    tickDuration: number;
    restDuration: number;
    constructor(fields: any);
    /**
     * Builds int array for this event.
     * @return {NoteEvent}
     */
    buildData(): NoteEvent;
}
export { NoteEvent };
