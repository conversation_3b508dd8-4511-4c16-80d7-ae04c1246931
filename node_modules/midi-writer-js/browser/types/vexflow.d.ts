import { Track } from './chunks/track';
declare class VexFlow {
    /**
     * Support for converting VexFlow voice into MidiWriterJS track
     * @return MidiWriter.Track object
     */
    trackFromVoice(voice: any, options?: {
        addRenderedAccidentals: boolean;
    }): Track;
    /**
     * Converts VexFlow pitch syntax to MidiWriterJS syntax
     * @param pitch string
     * @param index pitch index
     * @param note struct from Vexflow
     * @param addRenderedAccidentals adds Vexflow rendered accidentals
     */
    convertPitch(pitch: any, index: any, note: any, addRenderedAccidentals?: boolean): any;
    /**
     * Converts VexFlow duration syntax to MidiWriterJS syntax
     * @param note struct from VexFlow
     */
    convertDuration(note: any): string;
    /**
     * Converts VexFlow base duration syntax to MidiWriterJS syntax
     * @param duration Vexflow duration
     * @returns MidiWriterJS duration
     */
    convertBaseDuration(duration: string): string;
}
export { VexFlow };
