{"name": "@tonaljs/pitch-note", "version": "6.1.0", "description": "Parse intervals in shorthand notation", "keywords": ["note", "music", "theory", "interval", "shorthand notation"], "dependencies": {"@tonaljs/pitch": "5.0.2"}, "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "files": ["dist"], "author": "<EMAIL>", "license": "MIT", "publishConfig": {"access": "public"}, "jest": {"preset": "ts-jest", "testEnvironment": "node"}, "scripts": {"build": "tsup index.ts --sourcemap --dts --format esm,cjs", "test": "jest --coverage"}}