import { Pitch, NamedPitch, PitchCoordinates } from '@tonaljs/pitch';

type NoteWithOctave = string;
type PcName = string;
type NoteName = NoteWithOctave | PcName;
type NoteLiteral = NoteName | Pitch | NamedPitch;
interface Note extends Pitch, NamedPitch {
    readonly empty: boolean;
    readonly name: NoteName;
    readonly letter: string;
    readonly acc: string;
    readonly pc: PcName;
    readonly chroma: number;
    readonly height: number;
    readonly coord: PitchCoordinates;
    readonly midi: number | null;
    readonly freq: number | null;
}
type NoteType = Note;
declare const stepToLetter: (step: number) => string;
declare const altToAcc: (alt: number) => string;
declare const accToAlt: (acc: string) => number;
/**
 * Given a note literal (a note name or a note object), returns the Note object
 * @example
 * note('Bb4') // => { name: "Bb4", midi: 70, chroma: 10, ... }
 */
declare function note(src: NoteLiteral): Note;
type NoteTokens = [string, string, string, string];
/**
 * @private
 */
declare function tokenizeNote(str: string): NoteTokens;
/**
 * @private
 */
declare function coordToNote(noteCoord: PitchCoordinates): Note;

export { type Note, type NoteLiteral, type NoteName, type NoteType, type NoteWithOctave, type PcName, accToAlt, altToAcc, coordToNote, note, stepToLetter, tokenizeNote };
