import { NoteName } from '@tonaljs/pitch-note';

type Midi = number;
declare function isMidi(arg: any): arg is Midi;
/**
 * Get the note midi number (a number between 0 and 127)
 *
 * It returns undefined if not valid note name
 *
 * @function
 * @param {string|number} note - the note name or midi number
 * @return {Integer} the midi number or undefined if not valid note
 * @example
 * import { toMidi } from '@tonaljs/midi'
 * toMidi("C4") // => 60
 * toMidi(60) // => 60
 * toMidi('60') // => 60
 */
declare function toMidi(note: NoteName | number): number | null;
/**
 * Get the frequency in hertz from midi number
 *
 * @param {number} midi - the note midi number
 * @param {number} [tuning = 440] - A4 tuning frequency in Hz (440 by default)
 * @return {number} the frequency or null if not valid note midi
 * @example
 * import { midiToFreq} from '@tonaljs/midi'
 * midiToFreq(69) // => 440
 */
declare function midiToFreq(midi: number, tuning?: number): number;
/**
 * Get the midi number from a frequency in hertz. The midi number can
 * contain decimals (with two digits precision)
 *
 * @param {number} frequency
 * @return {number}
 * @example
 * import { freqToMidi} from '@tonaljs/midi'
 * freqToMidi(220)); //=> 57
 * freqToMidi(261.62)); //=> 60
 * freqToMidi(261)); //=> 59.96
 */
declare function freqToMidi(freq: number): number;
interface ToNoteNameOptions {
    pitchClass?: boolean;
    sharps?: boolean;
}
/**
 * Given a midi number, returns a note name. The altered notes will have
 * flats unless explicitly set with the optional `useSharps` parameter.
 *
 * @function
 * @param {number} midi - the midi note number
 * @param {Object} options = default: `{ sharps: false, pitchClass: false }`
 * @param {boolean} useSharps - (Optional) set to true to use sharps instead of flats
 * @return {string} the note name
 * @example
 * import { midiToNoteName } from '@tonaljs/midi'
 * midiToNoteName(61) // => "Db4"
 * midiToNoteName(61, { pitchClass: true }) // => "Db"
 * midiToNoteName(61, { sharps: true }) // => "C#4"
 * midiToNoteName(61, { pitchClass: true, sharps: true }) // => "C#"
 * // it rounds to nearest note
 * midiToNoteName(61.7) // => "D4"
 */
declare function midiToNoteName(midi: number, options?: ToNoteNameOptions): string;
declare function chroma(midi: number): number;
/**
 * Given a list of midi numbers, returns the pitch class set (unique chroma numbers)
 * @param midi
 * @example
 *
 */
declare function pcset(notes: number[] | string): number[];
declare function pcsetNearest(notes: number[] | string): (midi: number) => number | undefined;
declare function pcsetSteps(notes: number[] | string, tonic: number): (step: number) => number;
declare function pcsetDegrees(notes: number[] | string, tonic: number): (degree: number) => number | undefined;
/** @deprecated */
declare const _default: {
    chroma: typeof chroma;
    freqToMidi: typeof freqToMidi;
    isMidi: typeof isMidi;
    midiToFreq: typeof midiToFreq;
    midiToNoteName: typeof midiToNoteName;
    pcsetNearest: typeof pcsetNearest;
    pcset: typeof pcset;
    pcsetDegrees: typeof pcsetDegrees;
    pcsetSteps: typeof pcsetSteps;
    toMidi: typeof toMidi;
};

export { type ToNoteNameOptions, chroma, _default as default, freqToMidi, isMidi, midiToFreq, midiToNoteName, pcset, pcsetDegrees, pcsetNearest, pcsetSteps, toMidi };
