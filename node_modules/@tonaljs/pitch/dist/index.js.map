{"version": 3, "sources": ["../index.ts"], "sourcesContent": ["export interface NamedPitch {\n  readonly name: string;\n}\n\n/*** @deprecated use NamedPitch */\nexport interface Named {\n  readonly name: string;\n}\n\nexport interface NotFound extends NamedPitch {\n  readonly empty: true;\n  readonly name: \"\";\n}\n\nexport function isNamedPitch(src: unknown): src is NamedPitch {\n  return src !== null &&\n    typeof src === \"object\" &&\n    \"name\" in src &&\n    typeof src.name === \"string\"\n    ? true\n    : false;\n}\n\ntype Fifths = number;\ntype Octaves = number;\nexport type Direction = 1 | -1;\n\nexport type PitchClassCoordinates = [Fifths];\nexport type NoteCoordinates = [Fifths, Octaves];\nexport type IntervalCoordinates = [Fifths, Octaves, Direction];\nexport type PitchCoordinates =\n  | PitchClassCoordinates\n  | NoteCoordinates\n  | IntervalCoordinates;\n\n/**\n * Pitch properties\n *\n * - {number} step - The step number: 0 = C, 1 = D, ... 6 = B\n * - {number} alt - Number of alterations: -2 = 'bb', -1 = 'b', 0 = '', 1 = '#', ...\n * - {number} [oct] = The octave (undefined when is a coord class)\n * - {number} [dir] = Interval direction (undefined when is not an interval)\n */\nexport interface Pitch {\n  readonly step: number;\n  readonly alt: number;\n  readonly oct?: number; // undefined for pitch classes\n  readonly dir?: Direction; // undefined for notes\n}\n\nconst SIZES = [0, 2, 4, 5, 7, 9, 11];\nexport const chroma = ({ step, alt }: Pitch) => (SIZES[step] + alt + 120) % 12;\n\nexport const height = ({ step, alt, oct, dir = 1 }: Pitch) =>\n  dir * (SIZES[step] + alt + 12 * (oct === undefined ? -100 : oct));\n\nexport const midi = (pitch: Pitch) => {\n  const h = height(pitch);\n  return pitch.oct !== undefined && h >= -12 && h <= 115 ? h + 12 : null;\n};\n\nexport function isPitch(pitch: unknown): pitch is Pitch {\n  return pitch !== null &&\n    typeof pitch === \"object\" &&\n    \"step\" in pitch &&\n    typeof pitch.step === \"number\" &&\n    \"alt\" in pitch &&\n    typeof pitch.alt === \"number\" &&\n    !isNaN(pitch.step) &&\n    !isNaN(pitch.alt)\n    ? true\n    : false;\n}\n\n// The number of fifths of [C, D, E, F, G, A, B]\nconst FIFTHS = [0, 2, 4, -1, 1, 3, 5];\n// The number of octaves it span each step\nconst STEPS_TO_OCTS = FIFTHS.map((fifths: number) =>\n  Math.floor((fifths * 7) / 12),\n);\n\n/**\n * Get coordinates from pitch object\n */\nexport function coordinates(pitch: Pitch): PitchCoordinates {\n  const { step, alt, oct, dir = 1 } = pitch;\n  const f = FIFTHS[step] + 7 * alt;\n  if (oct === undefined) {\n    return [dir * f];\n  }\n  const o = oct - STEPS_TO_OCTS[step] - 4 * alt;\n  return [dir * f, dir * o];\n}\n\n// We need to get the steps from fifths\n// Fifths for CDEFGAB are [ 0, 2, 4, -1, 1, 3, 5 ]\n// We add 1 to fifths to avoid negative numbers, so:\n// for [\"F\", \"C\", \"G\", \"D\", \"A\", \"E\", \"B\"] we have:\nconst FIFTHS_TO_STEPS = [3, 0, 4, 1, 5, 2, 6];\n\n/**\n * Get pitch from coordinate objects\n */\nexport function pitch(coord: PitchCoordinates): Pitch {\n  const [f, o, dir] = coord;\n  const step = FIFTHS_TO_STEPS[unaltered(f)];\n  const alt = Math.floor((f + 1) / 7);\n  if (o === undefined) {\n    return { step, alt, dir };\n  }\n  const oct = o + 4 * alt + STEPS_TO_OCTS[step];\n  return { step, alt, oct, dir };\n}\n\n// Return the number of fifths as if it were unaltered\nfunction unaltered(f: number): number {\n  const i = (f + 1) % 7;\n  return i < 0 ? 7 + i : i;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAcO,SAAS,aAAa,KAAiC;AAC5D,SAAO,QAAQ,QACb,OAAO,QAAQ,YACf,UAAU,OACV,OAAO,IAAI,SAAS,WAClB,OACA;AACN;AA6BA,IAAM,QAAQ,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE;AAC5B,IAAM,SAAS,CAAC,EAAE,MAAM,IAAI,OAAc,MAAM,IAAI,IAAI,MAAM,OAAO;AAErE,IAAM,SAAS,CAAC,EAAE,MAAM,KAAK,KAAK,MAAM,EAAE,MAC/C,OAAO,MAAM,IAAI,IAAI,MAAM,MAAM,QAAQ,SAAY,OAAO;AAEvD,IAAM,OAAO,CAACA,WAAiB;AACpC,QAAM,IAAI,OAAOA,MAAK;AACtB,SAAOA,OAAM,QAAQ,UAAa,KAAK,OAAO,KAAK,MAAM,IAAI,KAAK;AACpE;AAEO,SAAS,QAAQA,QAAgC;AACtD,SAAOA,WAAU,QACf,OAAOA,WAAU,YACjB,UAAUA,UACV,OAAOA,OAAM,SAAS,YACtB,SAASA,UACT,OAAOA,OAAM,QAAQ,YACrB,CAAC,MAAMA,OAAM,IAAI,KACjB,CAAC,MAAMA,OAAM,GAAG,IACd,OACA;AACN;AAGA,IAAM,SAAS,CAAC,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,CAAC;AAEpC,IAAM,gBAAgB,OAAO;AAAA,EAAI,CAAC,WAChC,KAAK,MAAO,SAAS,IAAK,EAAE;AAC9B;AAKO,SAAS,YAAYA,QAAgC;AAC1D,QAAM,EAAE,MAAM,KAAK,KAAK,MAAM,EAAE,IAAIA;AACpC,QAAM,IAAI,OAAO,IAAI,IAAI,IAAI;AAC7B,MAAI,QAAQ,QAAW;AACrB,WAAO,CAAC,MAAM,CAAC;AAAA,EACjB;AACA,QAAM,IAAI,MAAM,cAAc,IAAI,IAAI,IAAI;AAC1C,SAAO,CAAC,MAAM,GAAG,MAAM,CAAC;AAC1B;AAMA,IAAM,kBAAkB,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAKrC,SAAS,MAAM,OAAgC;AACpD,QAAM,CAAC,GAAG,GAAG,GAAG,IAAI;AACpB,QAAM,OAAO,gBAAgB,UAAU,CAAC,CAAC;AACzC,QAAM,MAAM,KAAK,OAAO,IAAI,KAAK,CAAC;AAClC,MAAI,MAAM,QAAW;AACnB,WAAO,EAAE,MAAM,KAAK,IAAI;AAAA,EAC1B;AACA,QAAM,MAAM,IAAI,IAAI,MAAM,cAAc,IAAI;AAC5C,SAAO,EAAE,MAAM,KAAK,KAAK,IAAI;AAC/B;AAGA,SAAS,UAAU,GAAmB;AACpC,QAAM,KAAK,IAAI,KAAK;AACpB,SAAO,IAAI,IAAI,IAAI,IAAI;AACzB;", "names": ["pitch"]}