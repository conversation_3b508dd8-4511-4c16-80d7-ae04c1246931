interface NamedPitch {
    readonly name: string;
}
/*** @deprecated use NamedPitch */
interface Named {
    readonly name: string;
}
interface NotFound extends NamedPitch {
    readonly empty: true;
    readonly name: "";
}
declare function isNamedPitch(src: unknown): src is NamedPitch;
type Fifths = number;
type Octaves = number;
type Direction = 1 | -1;
type PitchClassCoordinates = [Fifths];
type NoteCoordinates = [Fifths, Octaves];
type IntervalCoordinates = [Fifths, Octaves, Direction];
type PitchCoordinates = PitchClassCoordinates | NoteCoordinates | IntervalCoordinates;
/**
 * Pitch properties
 *
 * - {number} step - The step number: 0 = C, 1 = D, ... 6 = B
 * - {number} alt - Number of alterations: -2 = 'bb', -1 = 'b', 0 = '', 1 = '#', ...
 * - {number} [oct] = The octave (undefined when is a coord class)
 * - {number} [dir] = Interval direction (undefined when is not an interval)
 */
interface Pitch {
    readonly step: number;
    readonly alt: number;
    readonly oct?: number;
    readonly dir?: Direction;
}
declare const chroma: ({ step, alt }: Pitch) => number;
declare const height: ({ step, alt, oct, dir }: Pitch) => number;
declare const midi: (pitch: Pitch) => number | null;
declare function isPitch(pitch: unknown): pitch is Pitch;
/**
 * Get coordinates from pitch object
 */
declare function coordinates(pitch: Pitch): PitchCoordinates;
/**
 * Get pitch from coordinate objects
 */
declare function pitch(coord: PitchCoordinates): Pitch;

export { type Direction, type IntervalCoordinates, type Named, type NamedPitch, type NotFound, type NoteCoordinates, type Pitch, type PitchClassCoordinates, type PitchCoordinates, chroma, coordinates, height, isNamedPitch, isPitch, midi, pitch };
