/* 虚拟控制按钮样式 */
.virtual-controls {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    justify-content: space-between;
    pointer-events: none; /* 允许点击穿透到游戏区域 */
    z-index: 1000;
}

.player-controls {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 20px;
    pointer-events: auto; /* 恢复按钮的点击事件 */
    width: 150px; /* 控制宽度 */
}

.player1-controls {
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
}

.player2-controls {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%) rotate(180deg);
}

.player-label {
    font-weight: bold;
    margin-bottom: 15px;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.7);
}

.direction-buttons {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: repeat(3, 1fr);
    gap: 8px;
    margin-bottom: 20px;
}

.direction-btn {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    border: none;
    background-color: rgba(255, 255, 255, 0.7);
    font-size: 28px;
    display: flex;
    justify-content: center;
    align-items: center;
    -webkit-tap-highlight-color: transparent;
}

.up-btn {
    grid-column: 2;
    grid-row: 1;
}

.left-btn {
    grid-column: 1;
    grid-row: 2;
}

.right-btn {
    grid-column: 3;
    grid-row: 2;
}

.down-btn {
    grid-column: 2;
    grid-row: 3;
}

.shoot-button-container {
    width: 100%;
    display: flex;
    justify-content: center;
    margin-top: 10px;
}

.shoot-btn {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    border: none;
    background-color: rgba(255, 255, 255, 0.7);
    font-size: 20px;
    font-weight: bold;
    display: flex;
    justify-content: center;
    align-items: center;
    -webkit-tap-highlight-color: transparent;
}

.direction-btn:active,
.shoot-btn:active {
    background-color: rgba(200, 200, 200, 0.9);
    transform: scale(0.95);
}

.player2-controls .direction-btn:active {
    transform: scale(0.95) rotate(180deg);
}

.player1-controls .direction-btn,
.player1-controls .shoot-btn {
    border: 3px solid #FF5555;
}

.player2-controls .direction-btn,
.player2-controls .shoot-btn {
    border: 3px solid #5555FF;
}

/* 适配不同屏幕尺寸 */
@media (max-height: 600px) {
    .direction-btn {
        width: 50px;
        height: 50px;
        font-size: 24px;
    }
    
    .shoot-btn {
        width: 70px;
        height: 70px;
        font-size: 18px;
    }
}