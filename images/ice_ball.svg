<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100">
  <!-- 背景光晕效果 -->
  <defs>
    <radialGradient id="glow" cx="50%" cy="50%" r="50%" fx="50%" fy="50%">
      <stop offset="0%" stop-color="#FFFFFF" stop-opacity="0.8"/>
      <stop offset="40%" stop-color="#A0E9FF" stop-opacity="0.6"/>
      <stop offset="100%" stop-color="#00BFFF" stop-opacity="0.2"/>
    </radialGradient>
    
    <!-- 冰球主体渐变 -->
    <radialGradient id="iceGradient" cx="40%" cy="40%" r="60%" fx="25%" fy="25%">
      <stop offset="0%" stop-color="#FFFFFF"/>
      <stop offset="50%" stop-color="#A0E9FF"/>
      <stop offset="100%" stop-color="#00BFFF"/>
    </radialGradient>
    
    <!-- 冰晶纹理 -->
    <filter id="iceTexture" x="-20%" y="-20%" width="140%" height="140%">
      <feTurbulence type="fractalNoise" baseFrequency="0.1" numOctaves="3" seed="5" result="noise"/>
      <feDisplacementMap in="SourceGraphic" in2="noise" scale="5" xChannelSelector="R" yChannelSelector="G"/>
    </filter>
  </defs>
  
  <!-- 外部光晕 -->
  <circle cx="50" cy="50" r="45" fill="url(#glow)"/>
  
  <!-- 冰球主体 -->
  <circle cx="50" cy="50" r="35" fill="url(#iceGradient)" filter="url(#iceTexture)"/>
  
  <!-- 冰晶结构 -->
  <g stroke="#FFFFFF" stroke-width="1.5" opacity="0.7">
    <!-- 水平冰晶 -->
    <line x1="20" y1="50" x2="80" y2="50" stroke-opacity="0.8"/>
    <!-- 垂直冰晶 -->
    <line x1="50" y1="20" x2="50" y2="80" stroke-opacity="0.8"/>
    <!-- 对角冰晶 -->
    <line x1="30" y1="30" x2="70" y2="70" stroke-opacity="0.6"/>
    <line x1="30" y1="70" x2="70" y2="30" stroke-opacity="0.6"/>
  </g>
  
  <!-- 高光效果 -->
  <circle cx="35" cy="35" r="5" fill="#FFFFFF" opacity="0.8"/>
  <circle cx="40" cy="40" r="2" fill="#FFFFFF" opacity="0.6"/>
  
  <!-- 冰晶碎片 -->
  <g fill="#FFFFFF" opacity="0.7">
    <polygon points="60,25 65,20 63,28" />
    <polygon points="75,50 82,48 78,55" />
    <polygon points="60,75 65,80 58,78" />
    <polygon points="25,50 18,48 22,55" />
  </g>
</svg>